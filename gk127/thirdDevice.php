<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);



if(isset($_GET['action']))
    {
        if($_GET['action'] == "refresh")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"delete from macAdd where uid='".$id."'");
                $message ='<div class="alert alert-danger" role="alert">
                    Student Devices re-freshed
                </div>';    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid student
                </div>';
            }    
        }
    }

?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                    <h2>Devices Refresh</h2>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                <div class="table-responsive">
                        <table class="table table2" id="myTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                        $query = "SELECT users.id as `id`,fname,lname,email,phone,DATE_FORMAT(thirdDevice.created, '%d/%m/%Y') as date,username,IF(`status`=2,'Inactive','Active') AS `status`, IF(`type`=1,'Admin','User') AS `type` FROM users,thirdDevice where `users`.`id`=`thirdDevice`.`uid` and `type`=2";
                        // echo $query;
                        $result = mysqli_query($connection,$query);
                        $id = 1;
                        while($rows=mysqli_fetch_array($result))
                        {
                            
                                
                            
                            echo "<tr>
                                    <td>".$rows['fname']."</td>
                                    <td>".$rows['email']."</td>
                                    <td>".$rows['phone']."</td>
                                    <td>".$rows['date']."</td>
                                    
                                    
                                  </tr>";
                            $id++;
                        }
                        ?>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>