<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_GET['action']))
    {
        if($_GET['action'] == "add")
        {
            $form = "add";
        }
        elseif($_GET['action'] == "edit")
        {
            $form = "edit";
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id,fname,lname,type,email,username,status,phone,city,cnic from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $results    = mysqli_fetch_array($strSQL);
                // print_r($results);
                $fname      = $results['fname'];
                $lname      = $results['lname'];
                $email      = $results['email'];
                $username   = $results['username'];
                $phone      = $results['phone'];
                $status     = $results['status'];
                $type       = $results['type'];
                $city       = $results['city'];
                $cnic       = $results['cnic'];
                // $access     = $results['access'];
                
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid user
                </div>';
                unset($form);
            }
        }
        elseif($_GET['action'] == "delete")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"delete from users where id='".$id."'");
                $strSQL = mysqli_query($connection,"delete from enrolled where userid='".$id."'");
                $message ='<div class="alert alert-danger" role="alert">
                    Student deleted
                </div>';    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid student
                </div>';
            }    
        }
        elseif($_GET['action'] == "suspend")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"update users set status=2 where id='".$id."'");
                $message ='<div class="alert alert-warning" role="alert">
                    Student suspend
                </div>';    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid user
                </div>';
            }    
        }
        elseif($_GET['action'] == "active")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"update users set status=1 where id='".$id."'");
                $strSQL = mysqli_query($connection,"delete from macAdd where uid='".$id."'");
                $message ='<div class="alert alert-success" role="alert">
                    Student activated
                </div>';    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid stdent
                </div>';
            }    
        }
        if($_GET['action'] == "refresh")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"delete from macAdd where uid='".$id."'");
                $message ='<div class="alert alert-danger" role="alert">
                    Student Devices re-freshed
                </div>';    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid student
                </div>';
            }    
        }
    }
    if(isset($_POST['action']))
    {
        if($_POST['action'] == "add")
        {
            
            $fname       = mysqli_real_escape_string($connection,$_POST['fname']);
            $lname       = mysqli_real_escape_string($connection,$_POST['lname']);
            $email       = mysqli_real_escape_string($connection,$_POST['email']);
            $username    = mysqli_real_escape_string($connection,$_POST['username']);
            $phone       = mysqli_real_escape_string($connection,$_POST['phone']);
            $status      = mysqli_real_escape_string($connection,$_POST['status']);
            $password    = mysqli_real_escape_string($connection,$_POST['password']);
            $city        = mysqli_real_escape_string($connection,$_POST['city']);
            $cnic        = mysqli_real_escape_string($connection,$_POST['cnic']);
            
            // $passwordRW  = substr(md5(microtime()),rand(0,26),6);
            $passwordRW  = $password;
            $password    = password_hash($passwordRW, PASSWORD_DEFAULT);

            $query = "SELECT email FROM users where email='".$email."'";
            $result = mysqli_query($connection,$query);
            $numResults = mysqli_num_rows($result);

            $query = "SELECT username FROM users where username='".$username."'";
            $resultUsr = mysqli_query($connection,$query);
            $numResultsUsr = mysqli_num_rows($resultUsr);
            if($numResultsUsr > 0)
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Username not available
                </div>';
                $form = "add";
            }
            elseif(!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid email address please type a valid email
                </div>';
                $form = "add";
            }
            elseif($numResults>0)
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Account already exist
                </div>';
                $form = "add";
            }
            else
            {
                mysqli_query($connection,"insert into users(fname,lname,email,password,phone,status,username,type,city,cnic) values('".$fname."','".$lname."','".$email."','".$password."','".$phone."','".$status."','".$username."',2,'".$city."','".$cnic."')");
                $message ='<div class="alert alert-success" role="alert">
                    Student Added
                </div>';
                $id = mysqli_insert_id($connection);

                for($i=0;$i<count($_POST['course']);$i++)
                {
                    mysqli_query($connection, "insert into 	enrolled(attempt,userid) values('" . $_POST['course'][$i] . "','" . $id . "')");
                }

                $encrypt = md5(90 * 13 + $id);
                $subject = "User Added";
                $from = webMaster;
                $body = 'Hello ' . $fname . ', <br/> <br/>Your account created on '.sitename.' please use below credentials to login.<br><br>Click here to login: ' . siteurl . '/login.php<br/> Email: '.$email.' <br/>Password: '.$passwordRW.' <br/><br/>--<br>' . siteurl;
                $headers = "From: " . strip_tags($from) . "\r\n";
                $headers .= "Reply-To: " . strip_tags($from) . "\r\n";
                $headers .= "CC: " . webMaster . "\r\n";
                $headers .= "MIME-Version: 1.0\r\n";
                $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                // echo $body;
                // if(host == "live")
                //   mail($to,$subject,$body,$headers);  
                
                unset($form);
            }
        }
        if($_POST['action'] == "edit")
        {
            $error = false;
            // print_r($_POST);
            $fname       = mysqli_real_escape_string($connection,$_POST['fname']);
            $lname       = mysqli_real_escape_string($connection,$_POST['lname']);
            $email       = mysqli_real_escape_string($connection,$_POST['email']);
            $username    = mysqli_real_escape_string($connection,$_POST['username']);
            $phone       = mysqli_real_escape_string($connection,$_POST['phone']);
            $status      = mysqli_real_escape_string($connection,$_POST['status']);
            $city        = mysqli_real_escape_string($connection,$_POST['city']);
            $cnic        = mysqli_real_escape_string($connection,$_POST['cnic']);
            // $access      = mysqli_real_escape_string($connection,$_POST['access']);
            if(isset($_POST['password']) && $_POST['password'] != "")
            {
                $password1    = password_hash(mysqli_real_escape_string($connection,$_POST['password']), PASSWORD_DEFAULT);
                $password    = ", password='".$password1."'";
            }
            $id     = mysqli_real_escape_string($connection,$_POST['id']);
            
            $query = "SELECT email,username FROM users where id='".$id."'";
            // echo $query;
            $result = mysqli_query($connection,$query);
            $results = mysqli_fetch_array($result);
            if(mysqli_num_rows($result) > 0)
            {
                if($results['username'] != $username)
                {
                    $query = "SELECT username FROM users where username='".$username."'";
                    $result = mysqli_query($connection,$query);
                    {
                        $numResults = mysqli_num_rows($result);
                        if($numResults>0)
                        {
                            $email = $results['email'];
                            $message ='<div class="alert alert-danger" role="alert">
                                Username not available
                            </div>';
                            $form = "edit";
                            $error = true;
                        }
                    }
                }
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
                {
                    $message ='<div class="alert alert-danger" role="alert">
                        Invalid email address please type a valid email
                    </div>';
                    $form = "add";
                    $error = true;
                }
                if($results['email'] != $email) 
                {
                    $query = "SELECT email FROM users where email='".$email."'";
                    $result = mysqli_query($connection,$query);
                    {
                        $numResults = mysqli_num_rows($result);
                        if($numResults>0)
                        {
                            $email = $results['email'];
                            $message ='<div class="alert alert-danger" role="alert">
                                Email address already exist
                            </div>';
                            $form = "edit";
                            $error = true;
                        }
                    }
                }
                if($error == false)
                {
                    $query = "update users set fname='$fname', lname='$lname', cnic='$cnic', city='$city', username='$username',phone='$phone', email='$email', status='$status' $password where id='".$id."'";
                    mysqli_query($connection, "DELETE FROM 	enrolled where userid='$id'");
                    for($i=0;$i<count($_POST['course']);$i++)
                    {
                        mysqli_query($connection, "insert into 	enrolled(attempt,userid) values('" . $_POST['course'][$i] . "','" . $id . "')");
                    }
                    $result = mysqli_query($connection,$query);
                    $message ='<div class="alert alert-success" role="alert">
                        Account updated
                    </div>';
                    unset($form);   
                }
                
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
                unset($form);
            }
        }
    }
?>
<style>
.dropdown-menu.show {
    overflow: overlay;
    height: 300px;
}
</style>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Students</h2>
                        <p class="mb-md-0">Students Management</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                  <a href="importStudents.php"  class="btn btn-primary mt-2 mt-xl-0  mr-2"><i class="mdi mdi-plus"></i> Import Student</a>
                  <a href="?action=add"  class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Student</a>
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; //echo $general->myJWT();?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                <?php if(isset($form)) {?>
                    <div class="container">
                <form id="form" action="students.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                  <div class="form-group">
                    First Name: <input type="text" name="fname" value="<?php echo isset($fname) ? $fname : ''; ?>" class="form-control form-control-lg" placeholder="First Name" required="required">
                  </div>
                  <div class="form-group">
                    Last Name: <input type="text" name="lname" value="<?php echo isset($lname) ? $lname : ''; ?>" class="form-control form-control-lg" placeholder="Last Name" required="required">
                  </div>
                  <div class="form-group">
                    Username: <input type="text" name="username" value="<?php echo isset($username) ? $username : ''; ?>" class="form-control form-control-lg" placeholder="Username" required="required">
                  </div>
                  <div class="form-group">
                    Email: <input type="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" class="form-control form-control-lg" placeholder="Email" required="required">
                  </div>
                  
                  <div class="form-group">
                    Password: <input type="password" name="password"  class="form-control form-control-lg" placeholder="Password" <?php if($form == "add"){ ?>required="required"<?php } ?>>
                  </div>
                  
                  <div class="form-group">
                    Phone: <input type="tel" pattern="[0-9]{11}" title="Phone number 11 digit with 0-9" name="phone" value="<?php echo isset($phone) ? $phone : ''; ?>" class="form-control form-control-lg" placeholder="Phone" required="required">
                  </div>
                  <div class="form-group">
                    CNIC: <small>Without dashes</small> <input type="tel" pattern="[0-9]{13}" title="CNIC 13 digit with 0-9 without dash" name="cnic" value="<?php echo isset($cnic) ? $cnic : ''; ?>" class="form-control form-control-lg" placeholder="CNIC ie: 4410123456343443" required="required">
                  </div>
                  <div class="form-group">
                      City: 
                    <select name="city" class="form-control form-control-lg" id="city" required>
                        <option value="" disabled selected>Select The City</option>
                        <option value="Islamabad">Islamabad</option>
                        <option value="Karachi">Karachi</option>
                        <option value="Lahore">Lahore</option>
                        <option value="Peshawar">Peshawar</option>
                        <option value="Quetta">Quetta</option>
                        <option value="" disabled>Punjab Cities</option>
                        <option value="Ahmed Nager Chatha">Ahmed Nager Chatha</option>
                        <option value="Ahmadpur East">Ahmadpur East</option>
                        <option value="Ali Khan Abad">Ali Khan Abad</option>
                        <option value="Alipur">Alipur</option>
                        <option value="Arifwala">Arifwala</option>
                        <option value="Attock">Attock</option>
                        <option value="Bhera">Bhera</option>
                        <option value="Bhalwal">Bhalwal</option>
                        <option value="Bahawalnagar">Bahawalnagar</option>
                        <option value="Bahawalpur">Bahawalpur</option>
                        <option value="Bhakkar">Bhakkar</option>
                        <option value="Burewala">Burewala</option>
                        <option value="Chillianwala">Chillianwala</option>
                        <option value="Chakwal">Chakwal</option>
                        <option value="Chichawatni">Chichawatni</option>
                        <option value="Chiniot">Chiniot</option>
                        <option value="Chishtian">Chishtian</option>
                        <option value="Daska">Daska</option>
                        <option value="Darya Khan">Darya Khan</option>
                        <option value="Dera Ghazi Khan">Dera Ghazi Khan</option>
                        <option value="Dhaular">Dhaular</option>
                        <option value="Dina">Dina</option>
                        <option value="Dinga">Dinga</option>
                        <option value="Dipalpur">Dipalpur</option>
                        <option value="Faisalabad">Faisalabad</option>
                        <option value="Ferozewala">Ferozewala</option>
                        <option value="Fateh Jhang">Fateh Jang</option>
                        <option value="Ghakhar Mandi">Ghakhar Mandi</option>
                        <option value="Gojra">Gojra</option>
                        <option value="Gujranwala">Gujranwala</option>
                        <option value="Gujrat">Gujrat</option>
                        <option value="Gujar Khan">Gujar Khan</option>
                        <option value="Hafizabad">Hafizabad</option>
                        <option value="Haroonabad">Haroonabad</option>
                        <option value="Hasilpur">Hasilpur</option>
                        <option value="Haveli Lakha">Haveli Lakha</option>
                        <option value="Jatoi">Jatoi</option>
                        <option value="Jalalpur">Jalalpur</option>
                        <option value="Jattan">Jattan</option>
                        <option value="Jampur">Jampur</option>
                        <option value="Jaranwala">Jaranwala</option>
                        <option value="Jhang">Jhang</option>
                        <option value="Jhelum">Jhelum</option>
                        <option value="Kalabagh">Kalabagh</option>
                        <option value="Karor Lal Esan">Karor Lal Esan</option>
                        <option value="Kasur">Kasur</option>
                        <option value="Kamalia">Kamalia</option>
                        <option value="Kamoke">Kamoke</option>
                        <option value="Khanewal">Khanewal</option>
                        <option value="Khanpur">Khanpur</option>
                        <option value="Kharian">Kharian</option>
                        <option value="Khushab">Khushab</option>
                        <option value="Kot Addu">Kot Addu</option>
                        <option value="Jauharabad">Jauharabad</option>
                        
                        <option value="Lalamusa">Lalamusa</option>
                        <option value="Layyah">Layyah</option>
                        <option value="Liaquat Pur">Liaquat Pur</option>
                        <option value="Lodhran">Lodhran</option>
                        <option value="Malakwal">Malakwal</option>
                        <option value="Mamoori">Mamoori</option>
                        <option value="Mailsi">Mailsi</option>
                        <option value="Mandi Bahauddin">Mandi Bahauddin</option>
                        <option value="Mian Channu">Mian Channu</option>
                        <option value="Mianwali">Mianwali</option>
                        <option value="Multan">Multan</option>
                        <option value="Murree">Murree</option>
                        <option value="Muridke">Muridke</option>
                        <option value="Mianwali Bangla">Mianwali Bangla</option>
                        <option value="Muzaffargarh">Muzaffargarh</option>
                        <option value="Narowal">Narowal</option>
                        <option value="Nankana Sahib">Nankana Sahib</option>
                        <option value="Okara">Okara</option>
                        <option value="Renala Khurd">Renala Khurd</option>
                        <option value="Pakpattan">Pakpattan</option>
                        <option value="Pattoki">Pattoki</option>
                        <option value="Pir Mahal">Pir Mahal</option>
                        <option value="Qaimpur">Qaimpur</option>
                        <option value="Qila Didar Singh">Qila Didar Singh</option>
                        <option value="Rabwah">Rabwah</option>
                        <option value="Raiwind">Raiwind</option>
                        <option value="Rajanpur">Rajanpur</option>
                        <option value="Rahim Yar Khan">Rahim Yar Khan</option>
                        <option value="Rawalpindi">Rawalpindi</option>
                        <option value="Sadiqabad">Sadiqabad</option>
                        <option value="Safdarabad">Safdarabad</option>
                        <option value="Sahiwal">Sahiwal</option>
                        <option value="Sangla Hill">Sangla Hill</option>
                        <option value="Sarai Alamgir">Sarai Alamgir</option>
                        <option value="Sargodha">Sargodha</option>
                        <option value="Shakargarh">Shakargarh</option>
                        <option value="Sheikhupura">Sheikhupura</option>
                        <option value="Sialkot">Sialkot</option>
                        <option value="Sohawa">Sohawa</option>
                        <option value="Soianwala">Soianwala</option>
                        <option value="Siranwali">Siranwali</option>
                        <option value="Talagang">Talagang</option>
                        <option value="Taxila">Taxila</option>
                        <option value="Toba Tek Singh">Toba Tek Singh</option>
                        <option value="Vehari">Vehari</option>
                        <option value="Wah Cantonment">Wah Cantonment</option>
                        <option value="Wazirabad">Wazirabad</option>
                        <option value="" disabled>Sindh Cities</option>
                        <option value="Badin">Badin</option>
                        <option value="Bhirkan">Bhirkan</option>
                        <option value="Rajo Khanani">Rajo Khanani</option>
                        <option value="Chak">Chak</option>
                        <option value="Dadu">Dadu</option>
                        <option value="Digri">Digri</option>
                        <option value="Diplo">Diplo</option>
                        <option value="Dokri">Dokri</option>
                        <option value="Ghotki">Ghotki</option>
                        <option value="Haala">Haala</option>
                        <option value="Hyderabad">Hyderabad</option>
                        <option value="Islamkot">Islamkot</option>
                        <option value="Jacobabad">Jacobabad</option>
                        <option value="Jamshoro">Jamshoro</option>
                        <option value="Jungshahi">Jungshahi</option>
                        <option value="Kandhkot">Kandhkot</option>
                        <option value="Kandiaro">Kandiaro</option>
                        
                        <option value="Kashmore">Kashmore</option>
                        <option value="Keti Bandar">Keti Bandar</option>
                        <option value="Khairpur">Khairpur</option>
                        <option value="Kotri">Kotri</option>
                        <option value="Larkana">Larkana</option>
                        <option value="Matiari">Matiari</option>
                        <option value="Mehar">Mehar</option>
                        <option value="Mirpur Khas">Mirpur Khas</option>
                        <option value="Mithani">Mithani</option>
                        <option value="Mithi">Mithi</option>
                        <option value="Mehrabpur">Mehrabpur</option>
                        <option value="Moro">Moro</option>
                        <option value="Nagarparkar">Nagarparkar</option>
                        <option value="Naudero">Naudero</option>
                        <option value="Naushahro Feroze">Naushahro Feroze</option>
                        <option value="Naushara">Naushara</option>
                        <option value="Nawabshah">Nawabshah</option>
                        <option value="Nazimabad">Nazimabad</option>
                        <option value="Qambar">Qambar</option>
                        <option value="Qasimabad">Qasimabad</option>
                        <option value="Ranipur">Ranipur</option>
                        <option value="Ratodero">Ratodero</option>
                        <option value="Rohri">Rohri</option>
                        <option value="Sakrand">Sakrand</option>
                        <option value="Sanghar">Sanghar</option>
                        <option value="Shahbandar">Shahbandar</option>
                        <option value="Shahdadkot">Shahdadkot</option>
                        <option value="Shahdadpur">Shahdadpur</option>
                        <option value="Shahpur Chakar">Shahpur Chakar</option>
                        <option value="Shikarpaur">Shikarpaur</option>
                        <option value="Sukkur">Sukkur</option>
                        <option value="Tangwani">Tangwani</option>
                        <option value="Tando Adam Khan">Tando Adam Khan</option>
                        <option value="Tando Allahyar">Tando Allahyar</option>
                        <option value="Tando Muhammad Khan">Tando Muhammad Khan</option>
                        <option value="Thatta">Thatta</option>
                        <option value="Umerkot">Umerkot</option>
                        <option value="Warah">Warah</option>
                        <option value="" disabled>KPK Cities</option>
                        <option value="Abbottabad">Abbottabad</option>
                        <option value="Adezai">Adezai</option>
                        <option value="Alpuri">Alpuri</option>
                        <option value="Akora Khattak">Akora Khattak</option>
                        <option value="Ayubia">Ayubia</option>
                        <option value="Banda Daud Shah">Banda Daud Shah</option>
                        <option value="Bannu">Bannu</option>
                        <option value="Batkhela">Batkhela</option>
                        <option value="Battagram">Battagram</option>
                        <option value="Birote">Birote</option>
                        <option value="Chakdara">Chakdara</option>
                        <option value="Charsadda">Charsadda</option>
                        <option value="Chitral">Chitral</option>
                        <option value="Daggar">Daggar</option>
                        <option value="Dargai">Dargai</option>
                        <option value="Darya Khan">Darya Khan</option>
                        <option value="Dera Ismail Khan">Dera Ismail Khan</option>
                        <option value="Doaba">Doaba</option>
                        <option value="Dir">Dir</option>
                        <option value="Drosh">Drosh</option>
                        <option value="Hangu">Hangu</option>
                        <option value="Haripur">Haripur</option>
                        <option value="Karak">Karak</option>
                        <option value="Kohat">Kohat</option>
                        <option value="Kulachi">Kulachi</option>
                        <option value="Lakki Marwat">Lakki Marwat</option>
                        <option value="Latamber">Latamber</option>
                        <option value="Madyan">Madyan</option>
                        <option value="Mansehra">Mansehra</option>
                        <option value="Mardan">Mardan</option>
                        <option value="Mastuj">Mastuj</option>
                        <option value="Mingora">Mingora</option>
                        <option value="Nowshera">Nowshera</option>
                        <option value="Paharpur">Paharpur</option>
                        <option value="Pabbi">Pabbi</option>
                        
                        <option value="Saidu Sharif">Saidu Sharif</option>
                        <option value="Shorkot">Shorkot</option>
                        <option value="Shewa Adda">Shewa Adda</option>
                        <option value="Swabi">Swabi</option>
                        <option value="Swat">Swat</option>
                        <option value="Tangi">Tangi</option>
                        <option value="Tank">Tank</option>
                        <option value="Thall">Thall</option>
                        <option value="Timergara">Timergara</option>
                        <option value="Tordher">Tordher</option>
                        <option value="" disabled>Balochistan Cities</option>
                        <option value="Awaran">Awaran</option>
                        <option value="Barkhan">Barkhan</option>
                        <option value="Chagai">Chagai</option>
                        <option value="Dera Bugti">Dera Bugti</option>
                        <option value="Gwadar">Gwadar</option>
                        <option value="Harnai">Harnai</option>
                        <option value="Jafarabad">Jafarabad</option>
                        <option value="Jhal Magsi">Jhal Magsi</option>
                        <option value="Kacchi">Kacchi</option>
                        <option value="Kalat">Kalat</option>
                        <option value="Kech">Kech</option>
                        <option value="Kharan">Kharan</option>
                        <option value="Khuzdar">Khuzdar</option>
                        <option value="Killa Abdullah">Killa Abdullah</option>
                        <option value="Killa Saifullah">Killa Saifullah</option>
                        <option value="Kohlu">Kohlu</option>
                        <option value="Lasbela">Lasbela</option>
                        <option value="Lehri">Lehri</option>
                        <option value="Loralai">Loralai</option>
                        <option value="Mastung">Mastung</option>
                        <option value="Musakhel">Musakhel</option>
                        <option value="Nasirabad">Nasirabad</option>
                        <option value="Nushki">Nushki</option>
                        <option value="Panjgur">Panjgur</option>
                        <option value="Pishin Valley">Pishin Valley</option>
                        
                        <option value="Sherani">Sherani</option>
                        <option value="Sibi">Sibi</option>
                        <option value="Sohbatpur">Sohbatpur</option>
                        <option value="Washuk">Washuk</option>
                        <option value="Zhob">Zhob</option>
                        <option value="Ziarat">Ziarat</option>
                    </select>
                  </div>
                  <div class="form-group formMult">
                        Course: <select name="course[]" id="course" multiple="multiple" class="form-control form-control-lg mt-2">
                            <?php
                            $query = "SELECT attempt.id as ID, course.title as Course,attempt.title as Attempt FROM `course`,`attempt` where course.id=attempt.courseID order by attempt.sortMe asc";
                            $result = mysqli_query($connection, $query);
                            
                            while ($rows = mysqli_fetch_array($result)) {
                                if($form == "edit")
                                {
                                    if($general->isEnrolled($id,$rows['ID'])) {
                                    ?>
                                    <option selected="selected" value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']." -> ".$rows['Attempt']; ?> Attempt</option>
                                    <?php
                                } 
                                else{
                                ?>
                                <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']." -> ".$rows['Attempt']; ?> Attempt</option>
                            <?php } } else { ?>
                                <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']." -> ".$rows['Attempt']; ?> Attempt</option>
                            <?php }  } ?>
                        </select>
                    </div>
                  <div class="form-group">
                      Account Status: <select name="status" id="status" class="form-control form-control-lg">
                          <option value="">Account Status </option>
                          <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                          <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                      </select>
                  </div>
                  <?php if($form == "edit"){ ?>
                          <input type="hidden" name="id" value="<?php echo $id; ?>">
                          <?php } ?>
                  <div class="mt-3">
                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                  </div>
                </form>
              </div>
                <?php }else{ ?>
                    <div class="table-responsive">
                    <form action="students.php" method="get" enctype="multipart/form-data" data-parsley-validate class="row form-horizontal form-label-left">
                    <div class="form-group col-lg-5">
                        <?php 
                        if(isset($_GET['search']) && $_GET['search'] == "search" && $_GET['filter'] != "")
                        {

                            $filter       = mysqli_real_escape_string($connection,$_GET['filter']);
                        ?>
                        <input style="height: 58px;margin-top: 7px;border-radius: 7px;" type="text" class="form-control" name="filter" value="<?php echo isset($filter) ? $filter : ''; ?>" aria-describedby="helpId" placeholder="Search by Name, Email or Phone">
                        <?php }else{ ?>
                            <input style="height: 58px;margin-top: 7px;border-radius: 7px;" type="text" class="form-control" name="filter"  aria-describedby="helpId" placeholder="Search by Name, Email or Phone">
                        <?php } ?>
                    </div>
                    <div class="form-group col-lg-7">
                    <div class="input-group" style="width: 95%;margin-left: 30px;">
                    <select name="courseSearch" class="form-control form-control-lg mt-2">
                        <option value="">Select Course</option>
                            <?php
                            $query = "SELECT attempt.id as ID, course.title as Course,attempt.title as Attempt FROM `course`,`attempt` where course.id=attempt.courseID order by attempt.sortMe asc";
                            $result = mysqli_query($connection, $query);
                            
                            while ($rows = mysqli_fetch_array($result)) 
                            {
                                if(isset($_GET['search']) && $_GET['search'] == "search" && $_GET['courseSearch'] == $rows['ID'])
                                {
                                    ?>
                                    <option selected="selected" value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']; ?> -> <?php echo $rows['Attempt']; ?></option>
                                    <?php
                                } 
                                else{
                                ?>
                                <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']; ?> -> <?php echo $rows['Attempt']; ?></option>
                                <?php 
                                }
                            }
                            ?>
                        </select>
                      <div class="input-group-append" style="margin: 7px 0px -1px 1px;">
                        <button class="btn btn-sm btn-primary" type="submit" name="search" value="search">Search</button>
                      </div>
                    </div>
                  </div>
                    
                    </form>
                    
                        <table class="table table2" id="myTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <!-- <th>Last Name</th> -->
                                    <th>Course</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <!-- <th>Created</th> -->
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                            $next = "";
                            if (isset($_GET['page']) && $_GET['page'] > 0) {
                                $page = mysqli_real_escape_string($connection, $_GET['page']);
                                $limit = $page * 100;
                            } else {
                                $limit = 0;
                                $page = 0;
                            }
                            if(isset($_GET['search']) && $_GET['search'] == "search")
                            {
                                if(isset($_GET['filter']) && $_GET['filter'] != ""){
                                    $filter       = mysqli_real_escape_string($connection,$_GET['filter']);
                                    $next = "filter=$filter&search=Search";
                                    
                                    $wc = " and (`U`.`fname` like '%$filter%'";
                                    $wc .= " or `U`.`lname` like '%$filter%'";
                                    $wc .= " or `U`.`email` like '%$filter%'";
                                    $wc .= " or `U`.`phone` like '%$filter%')";   
                                    $query2 = "SELECT `U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` FROM `users` as `U` where `type`=2 $wc limit $limit,100";                                 
                                }
                                if(isset($_GET['courseSearch']) && $_GET['courseSearch'] != ""){
                                    $courseSearch       = mysqli_real_escape_string($connection,$_GET['courseSearch']);
                                    $query2 = "SELECT `U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` FROM `enrolled` as `E`,`users` as `U` where E.userid=U.id and E.attempt=$courseSearch and `type`=2 $wc limit $limit,100";
                                }
                                if(!isset($query2))
                                {
                                    $query2 = "SELECT `U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` FROM `users` as `U` where `type`=2 limit $limit,100";
                                }
                            }
                            else
                            {
                                $query2 = "SELECT `U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` FROM `users` as `U` where `type`=2 limit $limit,100";
                            }

                        // echo $query2;
                        $result = mysqli_query($connection,$query2);
                        $id = 1;
                        while($rows=mysqli_fetch_array($result))
                        {
                            if($rows['id'] == 7 || $rows['id'] == 378)
                                continue;

                            if($rows['status'] == "Active")
                            {
                                $action1 = "<a href='?action=suspend&id=".$rows['id']."' onclick=\"return confirm('Are you sure you want to SUSPEND this Student?');\" class='badge badge-warning'><i class='fa fa-user-times'></i> Suspend </a>"; 
                            }
                            else {
                                $action1 = "<a href='?action=active&id=".$rows['id']."' onclick=\"return confirm('Are you sure you want to ACTIVATE this Student?');\" class='badge badge-success'><i class='fa fa-user'></i> Active </a>"; 
                            }
                            if($general->mobileAllowed($rows['id']) == true)
                            {
                                $action2 = "<a href='allowMobile.php?action=ban&id=".$rows['id']."' target='_blank' onclick=\"return confirm('Are you sure you want to disallow mobile access this Student?');\" class='badge badge-success'><i class='fa fa-phone'></i> DisAllow Phone </a>";
                            }
                            else
                            {
                                $action2 = "<a href='allowMobile.php?action=allow&id=".$rows['id']."' target='_blank' onclick=\"return confirm('Are you sure you want to Allow mobile access for this Student?');\" class='badge badge-success'><i class='fa fa-phone'></i> Allow Phone </a>"; 
                                
                            }
                            $action3 = "<a href='?action=refresh&id=".$rows['id']."' onclick=\"return confirm('Are you sure you want to Refressh Devices for this Student?');\" class='badge badge-warning'><i class='fa fa-computer'></i> Refresh </a>"; 
                            if($rows['id'] == 1 || userid == $rows['id'])
                            {
                                $action = "<a href='allowMobile.php?action=edit&id=".$rows['id']."' class='badge badge-info'><i class='fa fa-pencil'></i> Edit </a>";
                            }
                            else
                            {
                                $action = "<a href='?action=edit&id=".$rows['id']."' class='badge badge-info'><i class='fa fa-pencil'></i> Edit </a>
                                $action1 
                                $action2 
                                $action3
                                <a href='?action=delete&id=".$rows['id']."' onclick=\"return confirm('Are you sure you want to DELETE this Student?');\" class='badge badge-danger'><i class='fa fa-trash-o'></i> Delete </a>";
                            }
                            echo "<tr>
                                    <td>".$rows['fname']." ".$rows['lname']."</td>
                                    <td width='20%'>".$general->myCourses($rows['id'])."</td>
                                    <td>".$rows['email']."</td>
                                    <td>".$rows['phone']."</td>
                                    <td>".$rows['status']."</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                            $id++;
                        }
                        ?>
                                
                            </tbody>
                        </table>
                        <?php if (mysqli_num_rows($result) > 0) { ?>
                                <div class="text-center">
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination">
                                            <?php if ($page > 0) { ?>
                                                <li class="page-item"><a class="page-link" href="?<?php echo $next; ?>&page=<?php echo $page - 1; ?>">Previous</a></li>
                                            <?php } ?>

                                            <?php if (mysqli_num_rows($result) == 100) { ?>
                                                <li class="page-item"><a class="page-link" href="?<?php echo $next; ?>&page=<?php echo $page + 1; ?>">Next</a></li>
                                            <?php } ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php } ?>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
if(isset($city)){
    $citySelect = "$('#city').val('$city')";
}
$footerJS = '
<link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" />

<style>

.aweCheckbox {
    padding-left: 20px;
  }
  .aweCheckbox label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding: 0 20px 0 10px;
    cursor: pointer;
  }
  .aweCheckbox label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  }
  .aweCheckbox label::after {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    font-size: 11px;
    color: #555555;
  }
  .aweCheckbox input[type="checkbox"] {
    opacity: 0;
    z-index: 1;
  }
  .aweCheckbox input[type="checkbox"]:focus + label::before {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }
  .aweCheckbox input[type="checkbox"]:checked + label::after {
    font-family: "FontAwesome";
    content: "\f00c";
  }
  .aweCheckbox input[type="checkbox"]:indeterminate + label::after {
    display: block;
    content: "";
    width: 10px;
    height: 3px;
    background-color: #555555;
    border-radius: 2px;
    margin-left: -16.5px;
    margin-top: 7px;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label {
    opacity: 0.65;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label::before {
    background-color: #eeeeee;
    cursor: not-allowed;
  }
  .aweCheckbox.aweCheckbox-circle label::before {
    border-radius: 50%;
  }
  .aweCheckbox.aweCheckbox-inline {
    margin-top: 0;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::after {
    color: #fff;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::after {
    background-color: #fff;
  }
  input[type="checkbox"].styled:checked + label:after {
    font-family: \'FontAwesome\';
    content: "\f00c";
  }
  input[type="checkbox"] .styled:checked + label::before {
    color: #fff;
  }
  input[type="checkbox"] .styled:checked + label::after {
    color: #fff;
  }
  .formMult .btn-group{
    display: block;
    border: 1px solid grey;
}
.formMult .btn-group button{
    width: 100%;
    text-align: left;
}
</style>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/css/bootstrap-multiselect.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/js/bootstrap-multiselect.min.js"></script>






<script src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    '.$citySelect.'
    $("#course").multiselect({
      templates: { // Use the Awesome Bootstrap Checkbox structure
        li: \'<li class="checkList"><a tabindex="0"><div class="aweCheckbox aweCheckbox-danger"><label for=""></label></div></a></li>\'
      }
    });
    $(".multiselect-container div.aweCheckbox").each(function(index) {
  
      var id = "multiselect-" + index,
        $input = $(this).find("input");
  
      // Associate the label and the input
      $(this).find("label").attr("for", id);
      $input.attr("id", id);
  
      // Remove the input from the label wrapper
      $input.detach();
  
      // Place the input back in before the label
      $input.prependTo($(this));
  
      $(this).click(function(e) {
        // Prevents the click from bubbling up and hiding the dropdown
        e.stopPropagation();
      });
  
    });
  });

//   $(document).ready( function () {
//     $("#myTable").DataTable({
//         pageLength : 100,
//     lengthMenu: [[250, 500, 1000, -1], [250, 500, 1000, \'All\']],
//     dom: \'Bfrtip\',
//         buttons: [
//             \'copy\', \'csv\', \'excel\', \'pdf\', \'print\'
//         ]
//     });
//     $("#myTable td").css("white-space","initial");
// } );
</script>
';
include("includes/footer.php");
?>