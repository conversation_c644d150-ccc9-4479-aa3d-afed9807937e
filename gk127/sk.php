<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
if (userid == 2 || userid == 1) { } else {
    echo "some error occured please try again.";
    exit;
}
?>
<style>
.tw
{
    color:white;
}
</style>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Students Reports</h2>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">

                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; //echo $general->myJWT();
    ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="col-md-3 grid-margin">
                <div class="card d-flex align-items-center">
                    <div class="card-body">
                        <div class="tw">
                        Total Students Enrolled
                        </div>
                        <div class="tw text-center mt-2">
                            <h3><?php echo $general->totalStudents(); ?></h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>


</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>