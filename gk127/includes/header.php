<?php
    include_once('../includes/config.php');
    include_once('includes/session.php');
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title><?php echo sitename; ?></title>
  <!-- plugins:css -->
  <link rel="stylesheet" href="../assets/vendors/mdi/css/materialdesignicons.min.css">
  <link rel="stylesheet" href="../assets/vendors/base/vendor.bundle.base.css">
  <!-- endinject -->
  <!-- plugin css for this page -->
  <!-- End plugin css for this page -->
  <!-- inject:css -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="../assets/css/style.css">
  <link rel="stylesheet" href="../assets/css/style2.css">
  <!-- endinject -->
  <link rel="shortcut icon" href="../assets/images/favicon.png" />
  <?php 
$pic = array(1,2,3,4,5,6);
$k = array_rand($pic);
$v = $pic[$k];
$img = "../assets/images/class/$v.jpg"
  ?>
  <style>
.bgImage{
  
  background:linear-gradient(0deg, rgb(255 255 255 / 42%), rgb(64 96 50 / 22%)), url(<?php echo $img; ?>);
  background-position: center top;
  background-size: cover;
  position: relative;
  width: 100%;
  height: 100%;
}
.content-wrapper{
  background: none;
}
#ex4 .p1[data-count]:after {
  position: absolute;
  right: 25%;
  top: 20%;
  content: attr(data-count);
  font-size: 15px;
  padding: 0.2em;
  border-radius: 50%;
  line-height: 1em;
  color: white;
  background: rgba(255, 0, 0, 0.85);
  text-align: center;
  min-width: 1em;
}
</style>
  <?php echo isset($extraCSS) ? $extraCSS : ''; ?>

</head>

<body class="bgImage">
  <div class="container-scroller">
    <!-- partial:assets/partials/_navbar.html -->
    <nav class="navbar col-lg-12 col-12 p-0 fixed-top d-flex flex-row">
      <div class="navbar-brand-wrapper d-flex justify-content-center">
        <div class="navbar-brand-inner-wrapper d-flex justify-content-between align-items-center w-100">  
          <a class="navbar-brand brand-logo" href="dashboard.php"><img src="<?php echo websiteLogo;?>" alt="logo"/></a>
          <a class="navbar-brand brand-logo-mini" href="dashboard.php"><img src="<?php echo websiteMiniLogo;?>" alt="logo"/></a>
          <button class="navbar-toggler navbar-toggler align-self-center" type="button" data-toggle="minimize">
            <span class="mdi mdi-sort-variant"></span>
          </button>
        </div>  
      </div>
      <div class="navbar-menu-wrapper d-flex align-items-center justify-content-end">
        <!-- <ul class="navbar-nav mr-lg-4 w-100">
          <li class="nav-item nav-search d-none d-lg-block w-100">
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text" id="search">
                  <i class="mdi mdi-magnify"></i>
                </span>
              </div>
              <input type="text" class="form-control" placeholder="Search now" aria-label="search" aria-describedby="search">
            </div>
          </li>
        </ul> -->
        <ul class="navbar-nav navbar-nav-right">
        <li class="nav-item dropdown mr-4">
            <a style="margin-right: -30px;" class="nav-link count-indicator dropdown-toggle d-flex align-items-center justify-content-center notification-dropdown" id="notificationDropdown" href="#" data-toggle="dropdown" aria-expanded="false">
            <div id="ex4">
              <?php 
                $count      = 0;
                $query      = "SELECT count(*) as `notify` FROM `complainText` where  isReadA=1";
                $result     = mysqli_query($connection, $query);
                $numResults = mysqli_num_rows($result);
                $record     = mysqli_fetch_array($result);
                $count      = $record['notify'];
                ?>
                <?php if($count > 0){ ?>
  <span class="p1 fa-stack fa-2x has-badge" data-count="<?php echo $count; ?>">
    <i class="p3 fa fa-bell fa-stack-1x xfa-inverse" style="color:white;" data-count="4b"></i>
  </span>
                <?php }else{ ?>
                  <span class=" fa-stack fa-2x has-badge" data-count="4111">
    <i class="p3 fa fa-bell fa-stack-1x xfa-inverse" style="color:white;" data-count="4b"></i>
  </span>
                <?php } ?>
</div>
            </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown" aria-labelledby="notificationDropdown">
              <p class="mb-0 font-weight-normal float-left dropdown-header">Notifications</p>
              <?php if($count > 0){ ?>
              <a class="dropdown-item" href="contactus.php">
                <div class="item-thumbnail">
                  <div class="item-icon bg-success">
                    <i class="mdi mdi-message mx-0"></i>
                  </div>
                </div>
                <div class="item-content">
                  <h6 class="font-weight-normal">New Messages From Students</h6>
                  
                </div>
              </a>
              <?php }else{ ?>
                <a class="dropdown-item">
                <div class="item-thumbnail">
                  <div class="item-icon bg-warning">
                    <i class="mdi mdi-information mx-0"></i>
                  </div>
                </div>
                <div class="item-content">
                  <h6 class="font-weight-normal">No new Messages</h6>
                  
                </div>
              </a>
              <?php } ?>
            </div>
          </li>
          <li class="nav-item nav-profile dropdown">
            <a class="nav-link dropdown-toggle" href="#" data-toggle="dropdown" id="profileDropdown">
            <i class="fa fa-user" style="color: white; font-size: 30px;" alt="profile"></i>
              <span class="nav-profile-name"><?php echo fname." ".lname; ?></span>
            </a>
            <div class="dropdown-menu dropdown-menu-right navbar-dropdown" aria-labelledby="profileDropdown">
              <a href="myaccount.php" class="dropdown-item">
                <i class="mdi mdi-settings text-primary"></i>
                Settings
              </a>
              <a href="../logout.php" class="dropdown-item">
                <i class="mdi mdi-logout text-primary"></i>
                Logout
              </a>
            </div>
          </li>
        </ul>

        <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button" data-toggle="offcanvas">
          <span class="mdi mdi-menu" style="color:white;"></span>
        </button>
      </div>
    </nav>
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">
      <!-- partial:assets/partials/_sidebar.html -->
      <?php include_once('includes/sidebar.php') ?>
      <!-- partial -->
      <div class="main-panel">