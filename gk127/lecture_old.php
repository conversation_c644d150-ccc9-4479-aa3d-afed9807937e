<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

$page = 'Lecture';
$table = 'lecture';
// $query = "SELECT `id`,`created` FROM `lecture` WHERE start_time='1969-12-31 19:00:00'";
// $result = mysqli_query($connection, $query);
// while ($rows = mysqli_fetch_array($result)) {
//     $query = "UPDATE `lecture` set start_time = '".$rows['created']."' WHERE id = '".$rows['id']."'";
//     mysqli_query($connection, $query);
// }



if (isset($_REQUEST['courseID'])) {
    $courseID = mysqli_real_escape_string($connection, $_REQUEST['courseID']);
    $courseName = $general->getCourseName($courseID);
} else {
    echo "some error occured please try again.";
    exit;
}

if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $topic          = $results['topic'];
            $start_time     = $general->dateToDB($results['start_time']);
            $session        = $results['type'];
            $courseID       = $results['courseID'];
            $chapterID       = $results['chapterID'];
            $meetingID      = $results['meetingID'];
            
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
            unset($form);
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from $table where id='" . $id . "'");
            $message = '<div class="alert alert-danger" role="alert">
                ' . $page . ' deleted
                </div>';
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
        }
    }
}
if (isset($_POST['action'])) {

    if ($_POST['action'] == "add") {
        
        $topic          = mysqli_real_escape_string($connection, $_POST['topic']);
        $start_time     = mysqli_real_escape_string($connection, $_POST['start_time']);
        $type           = mysqli_real_escape_string($connection, $_POST['type']);
        $courseID       = mysqli_real_escape_string($connection, $_POST['courseID']);
        $chapterID      = mysqli_real_escape_string($connection, $_POST['chapterID']);
        $meetingID      = mysqli_real_escape_string($connection, $_POST['meetingID']);
        if($chapterID == "")
            $chapterID = 0;
        


        // create meeting on ZOOM
        // $meetingReturn = $general->createAMeeting($_POST);
        // $meetingReturn = json_decode($meetingReturn['content'],1);
        // $meetingID = $meetingReturn['id'];
        // echo "<pre>";
        // echo $meetingID;
        // echo "</pre>";
        // exit;
        // create meeting on ZOOM


        mysqli_query($connection, "insert into $table(`topic`, `courseID`, `chapterID`, `meetingID`,`type`,`start_time`) values('" . $topic . "', '" . $courseID . "','" . $chapterID . "', '" . $meetingID . "', '" . $type . "', '" . $start_time . "')");
        $message = '<div class="alert alert-success" role="alert">
            ' . $page . ' Added
            </div>';
        $id = mysqli_insert_id($connection);
        $_POST = array();
        unset($form);
    } elseif ($_POST['action'] == "edit") {
        $topic          = mysqli_real_escape_string($connection, $_POST['topic']);
        $start_time     = mysqli_real_escape_string($connection, $_POST['start_time']);
        $type           = mysqli_real_escape_string($connection, $_POST['type']);
        $courseID       = mysqli_real_escape_string($connection, $_POST['courseID']);
        $chapterID      = mysqli_real_escape_string($connection, $_POST['chapterID']);
        $meetingID      = mysqli_real_escape_string($connection, $_POST['meetingID']);
        

        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $query = "update $table set `topic`='$topic', `meetingID`='$meetingID',  `courseID`='$courseID', `start_time`='$start_time',  `chapterID`='$chapterID',  `type`='$type' where id='" . $id . "'";
        // echo $query;
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">
                ' . $page . ' updated
                </div>';
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
        unset($form);
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "sorting") {
        foreach ($_POST['newOrder'] as $key => $value) {
            $strSQL = mysqli_query($connection, "UPDATE $table set sortMe='$key' where id='" . $value . "'");
            // echo "UPDATE $table set sortMe='$key' where id='" . $value . "'"."\n";
            // echo "$key => $value \n";
        }

        exit;
    }
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Lectures in <?php echo $courseName; ?></h2>
                        <p class="mb-md-0">Manage your all Lectures of [<a href="course.php?classID=<?php echo $general->getClassIDbycourseID($courseID); ?>">BACK</a>]</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    <a href="?courseID=<?php echo $courseID; ?>&action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Lecture</a>
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <?php if (isset($form)) { ?>
                        <div class="container">
                            <form id="form" action="lecture.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                <div class="form-group">
                                    Topic: <input type="text" name="topic" value="<?php echo isset($topic) ? $topic : ''; ?>" class="form-control form-control-lg" placeholder="Topic" required="required">
                                </div>

                                <div class="form-group">
                                    Date: <input type="date" name="start_time" value="<?php echo isset($start_time) ? $start_time : ''; ?>" class="form-control form-control-lg" placeholder="Start Time" required="required" />
                                    
                                </div>

                                <div class="form-group">
                                    Session: <select name="type" id="session" class="form-control form-control-lg">
                                        <option <?php echo (isset($session) && $session == "1") ? 'selected="selected"' : ''; ?> value="1">Live</option>
                                        <option <?php echo (isset($session) && $session == "2") ? 'selected="selected"' : ''; ?> value="2">Recording</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    Chapter: <select name="chapterID" id="chapterID" class="form-control form-control-lg">
                                        <option value=""></option>
                                        <?php 
                                        $query = "SELECT * FROM `chapters` where courseID = '$courseID'";
                                        $result = mysqli_query($connection, $query);
                                        while ($rowch = mysqli_fetch_array($result)) {
                                            $selected = "";
                                            if(isset($chapterID) && $rowch['id'] == $chapterID)
                                                $selected = 'selected="selected';
                                            echo "<option value='".$rowch['id']."'>".$rowch['title']."</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                <span id="liveBlock">
                                    <div class="form-group">
                                        Meeting ID/Youtube ID: <input type="text" id="meetingID" name="meetingID" value="<?php echo isset($meetingID) ? $meetingID : ''; ?>" class="form-control form-control-lg" placeholder="Meeting ID" required="required">
                                    </div>


                                    <!--
                                    <div class="form-group">
                                        Duration: <input type="number" name="duration"  value="<?php echo isset($duration) ? $duration : ''; ?>" class="form-control form-control-lg" placeholder="Duration" required="required">
                                    </div> -->
                                </span>
                                <span id="recordingBlock" style="display: none;">
                                    <div class="form-group">
                                        Upload Lecture: <input type="file" id="lecture" name="lectureFile" disabled class="form-control form-control-lg" required="required">
                                    </div>
                                </span>

                                <input type="hidden" name="courseID" value="<?php echo $courseID; ?>">
                                <?php if ($form == "edit") { ?>
                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <?php } ?>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                </div>
                            </form>
                        </div>
                    <?php } else { ?>
                        <div class="table-responsive">
                            <table class="table table2">
                                <thead>
                                    <tr>
                                        <th>Topic</th>
                                        <th>Time</th>
                                        <th>Chapter</th>
                                        <th>Live/Recording</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="sortMe">
                                    <?php
                                        $query = "SELECT * FROM $table where courseID=$courseID order by sortMe,id";
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {

                                            // <a href='?action=edit&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                            $action = "
                                            <a href='addhandout.php?lectureid=" . $rows['id'] . "' class='badge badge-warning'><i class='mdi mdi-hand'></i> Handouts </a>
                                            <a href='addtest.php?lectureid=" . $rows['id'] . "' class='badge badge-success'><i class='mdi mdi-content-paste'></i> Test </a>
                                            <a href='?action=edit&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                            <a href='?action=delete&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to delete this lecture?');\" class='badge badge-danger'><i class='mdi mdi-delete'></i> Delete </a>";
                                            
                                            $recliv = "Recording";
                                            if($rows['type'] == 1)
                                            {
                                                $recliv = "Live";
                                            }
                                            echo "<tr data-id='" . $rows['id'] . "'>
                                    <td>" . $rows['topic'] . "</td>
                                    <td>" . $general->dateToRead($rows['start_time']) . "</td>
                                    <td>" . $general->getChapterName($rows['chapterID']) . "</td>
                                    <td>" . $recliv . "</td>
                                    
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                                            $id++;
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
$datepre = "";
if(isset($start_time)){
    $datepre = "'setDate':'$start_time',";
}
$footerJSLibs = '
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/js/tempusdominus-bootstrap-4.min.js" integrity="sha512-k6/Bkb8Fxf/c1Tkyl39yJwcOZ1P4cRrJu77p83zJjN2Z55prbFHxPs9vN7q3l3+tSMGPDdoH51AEU8Vgo1cgAA==" crossorigin="anonymous"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" integrity="sha512-3JRrEUwaCkFUBLK1N8HehwQgu8e23jTH4np5NHOmQOobuC4ROQxFwFgBLTnhcnQRMs84muMh0PnnwXlPq5MGjg==" crossorigin="anonymous" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.10.1/Sortable.min.js" integrity="sha256-9D6DlNlpDfh0C8buQ6NXxrOdLo/wqFUwEB1s70obwfE=" crossorigin="anonymous"></script>
<script>
$(function () {
    new Sortable(sortMe, {
        animation: 150,
        pull: \'clone\',
        ghostClass: \'blue-background-class\',
        
        store: {
            set: function (sortable) {
                var order = sortable.toArray();
                $.post(\'lecture.php?courseID='.$courseID.'\', {
                    action: \'sorting\',
                    newOrder: order
                },
                function(data, status) {
                    // console.log(data, \'foooooooooooooooooooooooo\');
                    
                });
                
            }
        }
        
    });

    $(\'#datetimepicker5\').datetimepicker({
        '.$datepre.'
        format: \'DD/MM/YYYY hh:mm A\'
        });
});

</script>';
include("includes/footer.php");
?>
<script>
    $("#session").change(function() {
        // if($(this).val() == 1)
        // {
        //     $("#liveBlock").show();
        //     $("#liveBlock :input").attr("disabled", false);

        //     $("#recordingBlock").hide();
        //     $("#recordingBlock :input").attr("disabled", true);
        // }
        // else if($(this).val() == 2)
        // {
        //     $("#liveBlock").hide();
        //     $("#liveBlock :input").attr("disabled", true);

        //     $("#recordingBlock").show();
        //     $("#recordingBlock :input").attr("disabled", false);
        // }
    })
</script>