<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
$page = 'Notification';
$table = 'notification';

if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $notification      = $results['notification'];
            $status      = $results['status'];
            
            
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
            unset($form);
        }
    } 
}
if (isset($_POST['action'])) {
    
    if ($_POST['action'] == "add") {
        $notification  = mysqli_real_escape_string($connection, $_POST['notification']);
        $status   = mysqli_real_escape_string($connection, $_POST['status']);
        
        mysqli_query($connection, "insert into $table(`notification`, `status`) values('" . $notification . "','" . $status . "')");
        $message = '<div class="alert alert-success" role="alert">
            ' . $page . ' Added
            </div>';
        $id = mysqli_insert_id($connection);
        $_POST = array();
        unset($form);
    }
    elseif ($_POST['action'] == "edit") {
        $notification  = mysqli_real_escape_string($connection, $_POST['notification']);
        $status   = mysqli_real_escape_string($connection, $_POST['status']);
        
        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $query = "update $table set `notification`='$notification', `status`='$status' where id='" . $id . "'";
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">
                ' . $page . ' updated
                </div>';
        $_POST = array();
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
        unset($form);
    }
}

?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Classes</h2>
                        <p class="mb-md-0">Manage Notification</p>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <?php if (isset($form)) { ?>
                        <div class="container">
                            <form id="form" action="notification.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                <div class="form-group">
                                    Notification: <textarea type="text" name="notification" class="form-control form-control-lg" placeholder="Notification" required="required"><?php echo isset($notification) ? $notification : ''; ?></textarea>
                                </div>
                                
                                <div class="form-group">
                                        <label for="status" class="control-label ">Status</label>
                                            <select id="status" class="form-control" name="status">
                                                <option value="0">Select Status</option>
                                                <option <?php echo (isset($status) && $status == 1) ? 'selected="selected"' : ''; ?> value="1">Active</option>
                                                <option <?php echo (isset($status) && $status == 2) ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                                            </select>
                                        
                                    </div>
                                
                                
                                
                                <?php if ($form == "edit") { ?>
                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <?php } ?>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                </div>
                            </form>
                        </div>
                    <?php } else { ?>
                        <div class="table-responsive">
                            <table class="table table2">
                                <thead>
                                    <tr>
                                        <th>Notification</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                        $query = "SELECT * FROM $table";
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {
                                            
                                            $action = "<a href='?action=edit&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>";
                                            if ($rows['status'] == 1)
                                                $status = "Active";
                                            if ($rows['status'] == 2)
                                                $status = "In-Active";
                                        echo "<tr>
                                    <td>" . $rows['notification'] . "</td>
                                    <td>" . $status . "</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                                            $id++;
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>