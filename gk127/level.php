<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
$page = 'Levels';
$table = 'levels';


if(isset($_REQUEST['classID']))
{
    $classID = mysqli_real_escape_string($connection,$_REQUEST['classID']);
    $className = $general->getClassName($classID);
}
else
{
    echo "some error occured please try again.";
    exit;
}


if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } 
    elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $title      = $results['title'];
            $desc       = $results['desc'];
            $classID       = $results['classID'];
            $status       = $results['status'];
            
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
            unset($form);
        }
    }
    elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $general->deleteLevel($id);
        $message = '<div class="alert alert-danger" role="alert">
            ' . $page . ' deleted
            </div>';
    } 
}
if (isset($_POST['action'])) {
    
    if ($_POST['action'] == "add") {
        $title  = mysqli_real_escape_string($connection, $_POST['title']);
        $desc   = mysqli_real_escape_string($connection, $_POST['desc']);
        $classID   = mysqli_real_escape_string($connection, $_POST['classID']);
        $status   = mysqli_real_escape_string($connection, $_POST['status']);
        
        mysqli_query($connection, "insert into $table(`title`, `desc`, `classID`, `status`) values('" . $title . "','" . $desc . "','" . $classID . "','" . $status . "')");
        $message = '<div class="alert alert-success" role="alert">
            ' . $page . ' Added
            </div>';
        $id = mysqli_insert_id($connection);
        $general->addLog(userid, 2, 1, "Level", json_encode($_POST), "", $id);
        $_POST = array();
        unset($form);
    }
    elseif ($_POST['action'] == "edit") {
        $title  = mysqli_real_escape_string($connection, $_POST['title']);
        $desc   = mysqli_real_escape_string($connection, $_POST['desc']);
        $classID   = mysqli_real_escape_string($connection, $_POST['classID']);
        $status   = mysqli_real_escape_string($connection, $_POST['status']);
        
        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $query = "update $table set `title`='$title', `desc`='$desc', `classID`='$classID', `status`='$status' where id='" . $id . "'";
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">
                ' . $page . ' updated
        </div>';
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        $results    = mysqli_fetch_assoc($strSQL);
        $general->addLog(userid, 2, 2, "Level", json_encode($_POST), json_encode($results), $id);
        $_POST = array();
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
        unset($form);
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "sorting") {
        foreach ($_POST['newOrder'] as $key => $value) {
            $strSQL = mysqli_query($connection, "UPDATE $table set sortMe='$key' where id='" . $value . "'");
            // echo "UPDATE $table set sortMe='$key' where id='" . $value . "'"."\n";
            // echo "$key => $value \n";
        }
        $general->addLog(userid, 2, 4, "Level", "", "");
        exit;
    }
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                    <h2>Level in <?php echo $className; ?></h2>
                        <p class="mb-md-0">Manage your all Levels in Class [<a href="class.php">BACK</a>]</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    <a href="?classID=<?php echo $classID; ?>&action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Level   </a>
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <?php if (isset($form)) { ?>
                        <div class="container">
                            <form id="form" action="level.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                <div class="form-group">
                                    Title: <input type="text" name="title" value="<?php echo isset($title) ? $title : ''; ?>" class="form-control form-control-lg" placeholder="ACCA" required="required">
                                </div>
                                <div class="form-group">
                                    Description: <textarea type="text" name="desc" class="form-control form-control-lg" placeholder="Description" required="required"><?php echo isset($desc) ? $desc : ''; ?></textarea>
                                </div>
                                <div class="form-group">
                                     Status: <select name="status" id="status" class="form-control form-control-lg">
                                        <option value=""> Status </option>
                                        <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                                        <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                                    </select>
                                </div>
                                <input type="hidden" name="classID" value="<?php echo $classID; ?>">
                                <?php if ($form == "edit") { ?>
                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <?php } ?>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                </div>
                            </form>
                        </div>
                    <?php } else { ?>
                        <div class="table-responsive">
                            <table class="table table2">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Desc</th>
                                        <th>Status</th>
                                        <th>Course</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="sortMe">
                                    <?php
                                        $query = "SELECT * FROM $table where classID=$classID order by sortMe";
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {
                                            if($rows['status'] == 1)
                                            $status="Active";
                                            else
                                            $status="Inactive";
                                            $action = "<a href='?action=edit&classID=" . $rows['classID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>";
                                            if(userid == 2 || userid == 2713){ 
                                                $action = "<a href='?action=edit&classID=" . $rows['classID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                                <a href='?action=delete&classID=" . $rows['classID'] . "&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to delete this Level?');\" class='badge badge-danger'><i class='mdi mdi-delete'></i> Delete </a>";
                                            }
                                            
                                echo "<tr data-id='" . $rows['id'] . "'>
                                    <td>" . $rows['title'] . "</td>
                                    <td>" . $rows['desc'] . "</td>
                                    <td>" . $status . "</td>

                                    <td>".$general->getCourseCount($rows['id']) . " &nbsp;<a href='course.php?levelID=" . $rows['id'] . "' class='badge badge-warning'><i class='mdi mdi-plus'></i> Add Course </a></td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                                            $id++;
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php

$footerJSLibs = '
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.10.1/Sortable.min.js" integrity="sha256-9D6DlNlpDfh0C8buQ6NXxrOdLo/wqFUwEB1s70obwfE=" crossorigin="anonymous"></script>
<script>
$(function () {
    new Sortable(sortMe, {
        animation: 150,
        pull: \'clone\',
        ghostClass: \'blue-background-class\',
        
        store: {
            set: function (sortable) {
                var order = sortable.toArray();
                $.post(\'level.php?classID='.$classID.'\', {
                    action: \'sorting\',
                    newOrder: order
                },
                function(data, status) {
                    // console.log(data, \'foooooooooooooooooooooooo\');
                    
                });
                
            }
        }
        
    });
        
});

</script>';
include("includes/footer.php");
?>