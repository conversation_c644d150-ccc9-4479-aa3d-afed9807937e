<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

$page = 'Attempt';
$table = 'attempt';

if(isset($_REQUEST['courseID']))
{
    $courseID = mysqli_real_escape_string($connection,$_REQUEST['courseID']);
    $className = $general->getCourseName($courseID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $title      = $results['title'];
            $desc       = $results['desc'];
            $courseID       = $results['courseID'];
            $status       = $results['status'];
            $end_date       = $results['end_date'];
            $syllabus       = $results['syllabus'];
            $planner       = $results['planner'];
            $faculty       = $results['faculty'];

            // $isDemo       = false;
            if($results['isDemo'] == 1)
            $isDemo       = $results['isDemo'];
            
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
            unset($form);
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $general->deleteAttempt($id);
        $message = '<div class="alert alert-danger" role="alert">
            ' . $page . ' deleted
            </div>';
    } elseif ($_GET['action'] == "duplicate") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $original = mysqli_fetch_array($strSQL);

            // Create new title with "Copy of" prefix
            $newTitle = "Copy of " . $original['title'];

            // Copy files if they exist
            $newSyllabus = "";
            $newPlanner = "";

            if($original['syllabus'] != "") {
                $target_dir = "../assets/syllabus/";
                $temp = explode(".", $original['syllabus']);
                $newSyllabus = "syllabus_".round(microtime(true)).'.'. end($temp);
                if(file_exists($target_dir . $original['syllabus'])) {
                    copy($target_dir . $original['syllabus'], $target_dir . $newSyllabus);
                }
            }

            if($original['planner'] != "") {
                $target_dir = "../assets/planner/";
                $temp = explode(".", $original['planner']);
                $newPlanner = "planner_".round(microtime(true)).'.'. end($temp);
                if(file_exists($target_dir . $original['planner'])) {
                    copy($target_dir . $original['planner'], $target_dir . $newPlanner);
                }
            }

            // Insert duplicate record (set isDemo to 0 for duplicates)
            mysqli_query($connection, "insert into $table(`isDemo`,`title`, `desc`, `courseID`, `status`,`end_date`,`syllabus`,`planner`,`faculty`)
                                      values('0','" . mysqli_real_escape_string($connection, $newTitle) . "','" . mysqli_real_escape_string($connection, $original['desc']) . "','" . $original['courseID'] . "','" . $original['status'] . "','" . $original['end_date'] . "','" . $newSyllabus . "','" . $newPlanner . "','" . $original['faculty'] . "')");

            $newId = mysqli_insert_id($connection);
            $general->addLog(userid, 2, 1, "Attempt", "Duplicated from ID: " . $id, "", $newId);

            $message = '<div class="alert alert-success" role="alert">
                ' . $page . ' duplicated successfully
                </div>';
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                Invalid ' . $page . ' to duplicate
                </div>';
        }
    }
}
if (isset($_POST['action'])) {
    
    if ($_POST['action'] == "add") {
        $title  = mysqli_real_escape_string($connection, $_POST['title']);
        $desc   = mysqli_real_escape_string($connection, $_POST['desc']);
        $courseID  = mysqli_real_escape_string($connection, $_POST['courseID']);
        $status  = mysqli_real_escape_string($connection, $_POST['status']);
        $end_date  = mysqli_real_escape_string($connection, $_POST['end_date']);
        $faculty  = mysqli_real_escape_string($connection, $_POST['faculty']);
        $isDemo = 0;
        if(isset($_POST['isDemo']))
        {
            $isDemo   = mysqli_real_escape_string($connection, $_POST['isDemo']);
            mysqli_query($connection,"UPDATE $table set `isDemo` = '0' WHERE `courseID`='$courseID'");
        }

        $syllabus   = "";
        $planner    = "";
        if($_FILES["syllabus"]["name"] != "")
        {
            $target_dir = "../assets/syllabus/";
            $temp = explode(".", $_FILES["syllabus"]["name"]);

            $syllabus = "syllabus_".round(microtime(true)).'.'. end($temp);
            move_uploaded_file($_FILES["syllabus"]["tmp_name"], $target_dir.$syllabus); 
        }

        if($_FILES["planner"]["name"] != "")
        {
            $target_dir = "../assets/planner/";
            $temp = explode(".", $_FILES["planner"]["name"]);

            $planner = "planner_".round(microtime(true)).'.'. end($temp);
            move_uploaded_file($_FILES["planner"]["tmp_name"], $target_dir.$planner);  
        }
        
        
        
        
        mysqli_query($connection,   "insert into $table(`isDemo`,`title`, `desc`, `courseID`, `status`,`end_date`,`syllabus`,`planner`,`faculty`) 
                                    values('" . $isDemo . "','" . $title . "','" . $desc . "','" . $courseID . "','" . $status . "','".$end_date."','".$syllabus."','".$planner."','".$faculty."')");
        $message = '<div class="alert alert-success" role="alert">
            ' . $page . ' Added
            </div>';
        $id = mysqli_insert_id($connection);
        $general->addLog(userid, 2, 1, "Attempt", json_encode($_POST), "", $id);
        unset($form);
    }
    elseif ($_POST['action'] == "edit") {
        $title      = mysqli_real_escape_string($connection, $_POST['title']);
        $desc       = mysqli_real_escape_string($connection, $_POST['desc']);
        $courseID   = mysqli_real_escape_string($connection, $_POST['courseID']);
        $status     = mysqli_real_escape_string($connection, $_POST['status']);
        $end_date   = mysqli_real_escape_string($connection, $_POST['end_date']);
        $faculty   = mysqli_real_escape_string($connection, $_POST['faculty']);
        $isDemo = "";
        if(isset($_POST['isDemo']))
        {
            $isDemo   = mysqli_real_escape_string($connection, $_POST['isDemo']);
            $isDemo = "`isDemo` = '$isDemo',";
            mysqli_query($connection,"UPDATE $table set `isDemo` = '0' WHERE `courseID`='$courseID'");
        }
        
        $id         = mysqli_real_escape_string($connection, $_POST['id']);


        if ($_FILES["syllabus"]["name"] != "") {

            $target_dir = "../assets/syllabus/";
            $temp = explode(".", $_FILES["syllabus"]["name"]);

            $syllabus = "syllabus_".round(microtime(true)).'.'. end($temp);
            move_uploaded_file($_FILES["syllabus"]["tmp_name"], $target_dir.$syllabus);
            $syllabus = ", syllabus='$syllabus'";
        } else {
            $syllabus = "";
        }

        if ($_FILES["planner"]["name"] != "") {

            $target_dir = "../assets/planner/";
            $temp = explode(".", $_FILES["planner"]["name"]);

            $planner = "planner_".round(microtime(true)).'.'. end($temp);
            move_uploaded_file($_FILES["planner"]["tmp_name"], $target_dir.$planner);
            $planner = ", planner='$planner'";
        } else {
            $planner = "";
        }
        
        $query      = "update $table set $isDemo `title`='$title', `desc`='$desc', `courseID`='$courseID', `status`='$status',`end_date`='$end_date',`faculty`='$faculty' $syllabus $planner where id='" . $id . "'";
        // echo $query;
        $result     = mysqli_query($connection, $query);
        $message    = '<div class="alert alert-success" role="alert">
                ' . $page . ' updated
                </div>';
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        $results    = mysqli_fetch_assoc($strSQL);
        $general->addLog(userid, 2, 2, "Attempt", json_encode($_POST), json_encode($results), $id);
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
        unset($form);
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "sorting") {
        foreach ($_POST['newOrder'] as $key => $value) {
            $strSQL = mysqli_query($connection, "UPDATE $table set sortMe='$key' where id='" . $value . "'");
            // echo "UPDATE $table set sortMe='$key' where id='" . $value . "'"."\n";
            // echo "$key => $value \n";
        }
        $general->addLog(userid, 2, 4, "Attempt", "", "");
        exit;
    }
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Attempts in <?php echo $className; ?></h2>
                        <p class="mb-md-0">Manage your all Course in level [<a href="course.php?levelID=<?php echo $general->getlevelIDbycourseID($courseID); ?>">BACK</a>]</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    <a href="?courseID=<?php echo $courseID; ?>&action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Attempt</a>
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <?php if (isset($form)) { ?>
                        <div class="container"  id="sortMe">
                            <form id="form" action="attempt.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                <div class="form-group">
                                    Title: <input type="text" name="title" value="<?php echo isset($title) ? $title : ''; ?>" class="form-control form-control-lg" placeholder="June 2021" required="required">
                                </div>
                                <div class="form-group">
                                    Description: <textarea type="text" name="desc" class="form-control form-control-lg" placeholder="Description" required="required"><?php echo isset($desc) ? $desc : ''; ?></textarea>
                                </div>
                                <div class="form-group">
                                    Course End Date: <input type="text" name="end_date" value="<?php echo isset($end_date) ? $end_date : ''; ?>" class="form-control form-control-lg" id="datetimepicker5" placeholder="Course End Date" required="required" />
                                </div>
                                <span>
                                    <div class="form-group">
                                        Upload Syllabus: <?php echo (isset($syllabus) && $syllabus != "") ? " <a href='../assets/syllabus/$syllabus'>View Uploaded File</a>" : ''; ?><input type="file" accept="application/pdf" name="syllabus" class="form-control form-control-lg">
                                        <small>* PDF Only</small>
                                    </div>
                                </span>
                                <span>
                                    <div class="form-group">
                                        Upload Planner: <?php echo (isset($planner) && $planner != "") ? " <a href='../assets/planner/$planner'>View Uploaded File</a>" : ''; ?> <input type="file" accept="application/pdf" name="planner" class="form-control form-control-lg">
                                        <small>* PDF Only</small>
                                    </div>
                                </span>
                                <div class="form-group">
                                     Faculty: <select name="faculty" id="faculty" class="form-control form-control-lg">
                                     <option value="">Select Faculty</option>
                                    <?php
                                    $query = "SELECT * from faculty";
                                    $result = mysqli_query($connection, $query);
                                    
                                    while ($rows = mysqli_fetch_array($result)) {
                                    if (isset($faculty) && $faculty == $rows['id']) {
                                        ?>
                                        <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                    <?php
                                        } else {
                                        ?>
                                        <option value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                    <?php }
                                    }
                                    ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                     Status: <select name="status" id="status" class="form-control form-control-lg">
                                        <option value=""> Status </option>
                                        <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                                        <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                                    </select>
                                </div>
                                <div class="form-check">
                                  <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" name="isDemo" id="isDemo" value="1" <?php echo isset($isDemo) ? 'checked' : ''; ?>>
                                    Show this attempt in demo?
                                  </label>
                                </div>
                               
                                <input type="hidden" name="courseID" value="<?php echo $courseID; ?>">
                                <?php if ($form == "edit") { ?>
                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <?php } ?>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                </div>
                            </form>
                        </div>
                    <?php } else { ?>
                        <div class="table-responsive">
                            <table class="table table2">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Desc</th>
                                        <th>End Date</th>
                                        <th>Files</th>
                                        <th>Faculty</th>
                                        <th>Status</th>
                                        <th>Attempt</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="sortMe">
                                    <?php
                                        $query = "SELECT * FROM $table where courseID=$courseID order by sortMe";
                                        $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {
                                            $resultFacultyname = "";
                                            if($rows['faculty'] > 0)
                                            {
                                                $query = "SELECT * FROM `faculty` where `id`='" . $rows['faculty'] . "'";
                                                $resultFaculty = mysqli_query($connection, $query);
                                                $resultFaculty = mysqli_fetch_array($resultFaculty);
                                                $resultFacultyname = $resultFaculty['name'];

                                            }

                                            if($rows['isDemo'] == 1)
                                                $demoBG = "bg-primary text-white";
                                            else
                                                $demoBG = "";
                                            if($rows['status'] == 1)
                                                $status="Active";
                                            else
                                                $status="Inactive";
                                            $action = "<a href='?action=edit&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>";
                                            if(userid == 2 || userid == 2713){ 
                                                $action = "<a href='?action=edit&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                                <a href='?action=delete&courseID=" . $rows['courseID'] . "&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to delete this course?');\" class='badge badge-danger'><i class='mdi mdi-delete'></i> Delete </a>";
                                            }
                                            
                                            
                                            echo "<tr class='".$demoBG."' data-id='" . $rows['id'] . "'>
                                                <td>" . $rows['title'] . "</td>
                                                <td>" . $rows['desc'] . "</td>
                                                <td>" . $general->dateToRead($rows['end_date']) . "</td>
                                                <td>" . $status . "</td>
                                                <td>" . $resultFacultyname . "</td>
                                                <td>
                                                    <a href='https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/syllabus/".$rows['syllabus']."' ' target='_blank'>Syllabus</a> | <a href='https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/syllabus/".$rows['planner']."' ' target='_blank'>Planner</a>
                                                </td>
                                                <td>" . $general->getChapterCount($rows['id']) . " <a href='chapters.php?attemptID=" . $rows['id'] . "' class='badge badge-warning'><i class='mdi mdi-plus'></i> Add Chapters </a> | <a href='booklets.php?attemptID=" . $rows['id'] . "' class='badge badge-success'><i class='mdi mdi-plus'></i> Booklet </a></td>
                                                <td>
                                                    $action
                                                </td>
                                                
                                            </tr>";
                                            
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
$footerJSLibs = '
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.10.1/Sortable.min.js" integrity="sha256-9D6DlNlpDfh0C8buQ6NXxrOdLo/wqFUwEB1s70obwfE=" crossorigin="anonymous"></script>
<script>
$(function () {
    new Sortable(sortMe, {
        animation: 150,
        pull: \'clone\',
        ghostClass: \'blue-background-class\',
        
        store: {
            set: function (sortable) {
                var order = sortable.toArray();
                $.post(\'attempt.php?courseID='.$courseID.'\', {
                    action: \'sorting\',
                    newOrder: order
                },
                function(data, status) {
                    // console.log(data, \'foooooooooooooooooooooooo\');
                    
                });
                
            }
        }
        
    });
        $("#datetimepicker5").daterangepicker({
            singleDatePicker: true,
            "autoApply": true,
            locale: {
                format: \'YYYY-MM-DD\'
              }
        });
});

</script>';
include("includes/footer.php");
?>