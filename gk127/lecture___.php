<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

$page = 'Lecture';
$table = 'lecture';

if (isset($_REQUEST['chapterID'])) {
    $chapterID = mysqli_real_escape_string($connection, $_REQUEST['chapterID']);
    $courseName = $general->getChapterName($chapterID);
} else {
    echo "some error occured please try again.";
    exit;
}

if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
    } elseif ($_GET['action'] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select * from $table where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            // print_r($results);
            $topic          = $results['topic'];
            $start_time     = $general->dateToDB($results['start_time']);
            $session        = $results['type'];
            $chapterID       = $results['chapterID'];
            $meetingID      = $results['meetingID'];
            $details      = $results['details'];
            $duration      = $results['duration'];
        } else {
            $message = '<div class="alert alert-danger" role="alert">
                    Invalid ' . $page . '
                </div>';
            unset($form);
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $general->deleteLecture($id);
        $message = '<div class="alert alert-danger" role="alert">
            ' . $page . ' deleted
            </div>';
    }
}
if (isset($_POST['action'])) {

    if ($_POST['action'] == "add") {
        $topic          = mysqli_real_escape_string($connection, $_POST['topic']);
        $start_time     = mysqli_real_escape_string($connection, $_POST['start_time']);
        $type           = 2;
        $chapterID       = mysqli_real_escape_string($connection, $_POST['chapterID']);
        $meetingID      = mysqli_real_escape_string($connection, $_POST['meetingID']);
        $details      = mysqli_real_escape_string($connection, $_POST['details']);
        $duration      = mysqli_real_escape_string($connection, $_POST['duration']);




        mysqli_query($connection, "insert into $table(`topic`, `chapterID`, `meetingID`,`type`,`start_time`,`details`,`duration`) values('" . $topic . "', '" . $chapterID . "', '" . $meetingID . "', '" . $type . "', '" . $start_time . "','" . $details . "','" . $duration . "')");
        $message = '<div class="alert alert-success" role="alert">
            ' . $page . ' Added
            </div>';
        $id = mysqli_insert_id($connection);
        $_POST = array();
        unset($form);
    } elseif ($_POST['action'] == "edit") {
        $topic          = mysqli_real_escape_string($connection, $_POST['topic']);
        $start_time     = mysqli_real_escape_string($connection, $_POST['start_time']);
        $type           = 2;
        $chapterID      = mysqli_real_escape_string($connection, $_POST['chapterID']);
        $meetingID      = mysqli_real_escape_string($connection, $_POST['meetingID']);
        $details      = mysqli_real_escape_string($connection, $_POST['details']);
        $duration      = mysqli_real_escape_string($connection, $_POST['duration']);


        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $query = "update $table set `topic`='$topic', `meetingID`='$meetingID',  `chapterID`='$chapterID', `start_time`='$start_time',  `type`='$type', `details` = '$details',`duration`='$duration' where id='" . $id . "'";
        // echo $query;
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">
                ' . $page . ' updated
                </div>';
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
        unset($form);
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "sorting") {
        foreach ($_POST['newOrder'] as $key => $value) {
            $strSQL = mysqli_query($connection, "UPDATE $table set sortMe='$key' where id='" . $value . "'");
            // echo "UPDATE $table set sortMe='$key' where id='" . $value . "'"."\n";
            // echo "$key => $value \n";
        }

        exit;
    }
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Lectures in <?php echo $courseName; ?></h2>
                        <p class="mb-md-0">Manage your all Lectures of [<a href="chapters.php?attemptID=<?php echo $general->getattemptIDbyChapterID($chapterID); ?>">BACK</a>]</p>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    <a href="lectureImport.php?chapterID=<?php echo $chapterID; ?>" class="btn btn-primary mt-2 mt-xl-0  mr-2"><i class="mdi mdi-plus"></i> Import Lectures</a>
                    <a href="?chapterID=<?php echo $chapterID; ?>&action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Lecture</a>
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <?php if (isset($form)) { ?>
                        <div class="container" id="sortMe">
                            <form id="form" action="lecture.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                <div class="form-group">
                                    Topic: <input type="text" name="topic" value="<?php echo isset($topic) ? $topic : ''; ?>" class="form-control form-control-lg" placeholder="Topic" required="required">
                                </div>
                                <div class="form-group">
                                    Details: <textarea type="text" name="details" class="form-control form-control-lg" placeholder="Details"><?php echo isset($details) ? $details : ''; ?></textarea>
                                </div>
                                <div class="form-group">
                                    Date: <input type="text" name="start_time" value="<?php echo isset($start_time) ? $start_time : ''; ?>" class="form-control form-control-lg" id="datetimepicker5" placeholder="Start Time" required="required" />
                                </div>
                                <div class="form-group">
                                    Duration:<small> Minutes</small> <input type="number" name="duration" value="<?php echo isset($duration) ? $duration : ''; ?>" class="form-control form-control-lg" placeholder="100" required="required" />
                                </div>

                                <!-- <div class="form-group">
                                    Session: <select name="type" id="session" class="form-control form-control-lg">
                                        <option <?php echo (isset($session) && $session == "1") ? 'selected="selected"' : ''; ?> value="1">Live</option>
                                        <option <?php echo (isset($session) && $session == "2") ? 'selected="selected"' : ''; ?> value="2">Recording</option>
                                    </select>
                                </div> -->
                                <span id="liveBlock">
                                    <div class="form-group">
                                        Youtube ID: <input type="text" id="meetingID" name="meetingID" value="<?php echo isset($meetingID) ? $meetingID : ''; ?>" class="form-control form-control-lg" placeholder="Youtube ID" required="required">
                                    </div>


                                    <!--
                                    <div class="form-group">
                                        Duration: <input type="number" name="duration"  value="<?php echo isset($duration) ? $duration : ''; ?>" class="form-control form-control-lg" placeholder="Duration" required="required">
                                    </div> -->
                                </span>
                                <span id="recordingBlock" style="display: none;">
                                    <div class="form-group">
                                        Upload Lecture: <input type="file" id="lecture" name="lectureFile" disabled class="form-control form-control-lg" required="required">
                                    </div>
                                </span>

                                <input type="hidden" name="chapterID" value="<?php echo $chapterID; ?>">
                                <?php if ($form == "edit") { ?>
                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                <?php } ?>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                </div>
                            </form>
                        </div>
                    <?php } else { ?>
                        <div class="table-responsive">
                            <table class="table table2" id="myTable">
                                <thead>
                                    <tr>
                                        <th>Topic</th>
                                        <th>Details</th>
                                        <th>Date</th>
                                        <th>Duration</th>
                                        <th>Handout</th>
                                        <th>Youtube ID</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="sortMe">
                                    <?php
                                        $query = "SELECT * FROM $table where chapterID=$chapterID order by sortMe";
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {

                                            // <a href='?action=edit&chapterID=" . $rows['chapterID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                            $action = "
                                            <a href='addhandout.php?lectureid=" . $rows['id'] . "' class='badge badge-warning'><i class='mdi mdi-hand'></i> Handouts </a>
                                            <a href='addtest.php?lectureid=" . $rows['id'] . "' class='badge badge-success'><i class='mdi mdi-content-paste'></i> Test </a>
                                            <a href='?action=edit&chapterID=" . $rows['chapterID'] . "&id=" . $rows['id'] . "' class='badge badge-info'><i class='mdi mdi-pencil'></i> Edit </a>
                                            <a href='?action=delete&chapterID=" . $rows['chapterID'] . "&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to delete this lecture?');\" class='badge badge-danger'><i class='mdi mdi-delete'></i> Delete </a>";


                                            echo "<tr data-id='" . $rows['id'] . "'>
                                    <td>" . $rows['topic'] . "</td>
                                    <td>" . $rows['details'] . "</td>
                                    <td>" . $general->dateToRead($rows['start_time']) . "</td>
                                    <td>" . date('H:i', mktime(0, $rows['duration'])) . "</td>
                                    <td>" . $general->getNumberOfHandoutInlecture($rows['id']) . " Handout(s)</td>
                                    <td>" . $rows['meetingID'] . "</td>
                                    
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                                            $id++;
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
// $datepre = "";
// if(isset($start_time)){
//     $datepre = "'setDate':'$start_time',";
// }
$footerJSLibs = '
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.22.2/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.10.1/Sortable.min.js" integrity="sha256-9D6DlNlpDfh0C8buQ6NXxrOdLo/wqFUwEB1s70obwfE=" crossorigin="anonymous"></script>

<link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet" />

<style>

.aweCheckbox {
    padding-left: 20px;
  }
  .aweCheckbox label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding: 0 20px 0 10px;
    cursor: pointer;
  }
  .aweCheckbox label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  }
  .aweCheckbox label::after {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    font-size: 11px;
    color: #555555;
  }
  .aweCheckbox input[type="checkbox"] {
    opacity: 0;
    z-index: 1;
  }
  .aweCheckbox input[type="checkbox"]:focus + label::before {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }
  .aweCheckbox input[type="checkbox"]:checked + label::after {
    font-family: "FontAwesome";
    content: "\f00c";
  }
  .aweCheckbox input[type="checkbox"]:indeterminate + label::after {
    display: block;
    content: "";
    width: 10px;
    height: 3px;
    background-color: #555555;
    border-radius: 2px;
    margin-left: -16.5px;
    margin-top: 7px;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label {
    opacity: 0.65;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label::before {
    background-color: #eeeeee;
    cursor: not-allowed;
  }
  .aweCheckbox.aweCheckbox-circle label::before {
    border-radius: 50%;
  }
  .aweCheckbox.aweCheckbox-inline {
    margin-top: 0;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::after {
    color: #fff;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::after {
    background-color: #fff;
  }
  input[type="checkbox"].styled:checked + label:after {
    font-family: \'FontAwesome\';
    content: "\f00c";
  }
  input[type="checkbox"] .styled:checked + label::before {
    color: #fff;
  }
  input[type="checkbox"] .styled:checked + label::after {
    color: #fff;
  }
  .formMult .btn-group{
    display: block;
    border: 1px solid grey;
}
.formMult .btn-group button{
    width: 100%;
    text-align: left;
}
</style>


<script src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<script>

$(function () {
    $("#myTable").DataTable({
        pageLength : 1000,
        "order": [],
    lengthMenu: [[250, 500, 1000, -1], [250, 500, 1000, \'All\']],
    dom: \'Bfrtip\',
        buttons: [
            \'copy\', \'csv\', \'excel\', \'pdf\', \'print\'
        ]
    });
    $("#myTable td").css("white-space","initial");

    new Sortable(sortMe, {
        animation: 150,
        pull: \'clone\',
        ghostClass: \'blue-background-class\',
        
        store: {
            set: function (sortable) {
                var order = sortable.toArray();
                $.post(\'lecture.php?chapterID=' . $chapterID . '\', {
                    action: \'sorting\',
                    newOrder: order
                },
                function(data, status) {
                    // console.log(data, \'foooooooooooooooooooooooo\');
                    
                });
                
            }
        }
        
    });
        $("#datetimepicker5").daterangepicker({
            singleDatePicker: true,
            "autoApply": true,
            locale: {
                format: \'YYYY-MM-DD\'
              }
        });
});

</script>';
include("includes/footer.php");
?>