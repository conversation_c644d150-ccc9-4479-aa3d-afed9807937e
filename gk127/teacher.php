<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_GET['action'])) {
  if ($_GET['action'] == "add") {
    $form = "add";
  } elseif ($_GET['action'] == "edit") {
    $form = "edit";
    $id       = mysqli_real_escape_string($connection, $_GET['id']);
    $strSQL = mysqli_query($connection, "select id,fname,lname,type,email,username,status,phone from users where id='" . $id . "' and type=3");
    if (mysqli_num_rows($strSQL) > 0) {
      $results    = mysqli_fetch_array($strSQL);
      // print_r($results);
      $fname      = $results['fname'];
      $lname      = $results['lname'];
      $email      = $results['email'];
      $username   = $results['username'];
      $phone      = $results['phone'];
      $status     = $results['status'];
      $type       = $results['type'];
      // $access     = $results['access'];

    } else {
      $message = '<div class="alert alert-danger" role="alert">
                    Invalid user
                </div>';
      unset($form);
    }
  } elseif ($_GET['action'] == "delete") {
    $id       = mysqli_real_escape_string($connection, $_GET['id']);
    $strSQL = mysqli_query($connection, "select id from users where id='" . $id . "'");
    if (mysqli_num_rows($strSQL) > 0) {
      $strSQL = mysqli_query($connection, "delete from users where id='" . $id . "'");
      $message = '<div class="alert alert-danger" role="alert">
      Teacher deleted
                </div>';
    } else {
      $message = '<div class="alert alert-danger" role="alert">
                    Invalid Teacher
                </div>';
    }
  }
  if ($_GET['action'] == "refresh") {
    $id       = mysqli_real_escape_string($connection, $_GET['id']);
    $strSQL = mysqli_query($connection, "select id from users where id='" . $id . "'");
    if (mysqli_num_rows($strSQL) > 0) {
      $strSQL = mysqli_query($connection, "delete from macAdd where uid='" . $id . "'");
      $message = '<div class="alert alert-danger" role="alert">
                    Teacher Devices re-freshed
                </div>';
    } else {
      $message = '<div class="alert alert-danger" role="alert">
                    Invalid Teacher
                </div>';
    }
  }
}

if (isset($_POST['action'])) {
  if ($_POST['action'] == "add") {

    $fname       = mysqli_real_escape_string($connection, $_POST['fname']);
    $lname       = mysqli_real_escape_string($connection, $_POST['lname']);
    $email       = mysqli_real_escape_string($connection, $_POST['email']);
    $username    = mysqli_real_escape_string($connection, $_POST['username']);
    $phone       = mysqli_real_escape_string($connection, $_POST['phone']);
    $status      = mysqli_real_escape_string($connection, $_POST['status']);
    $password      = mysqli_real_escape_string($connection, $_POST['password']);

    // $passwordRW  = substr(md5(microtime()),rand(0,26),6);
    $passwordRW  = $password;
    $password    = password_hash($passwordRW, PASSWORD_DEFAULT);

    $query = "SELECT email FROM users where email='" . $email . "' and type=3";
    $result = mysqli_query($connection, $query);
    $numResults = mysqli_num_rows($result);

    $query = "SELECT username FROM users where username='" . $username . "' and type=3";
    $resultUsr = mysqli_query($connection, $query);
    $numResultsUsr = mysqli_num_rows($resultUsr);
    if ($numResultsUsr > 0) {
      $message = '<div class="alert alert-danger" role="alert">
                    Username not available
                </div>';
      $form = "add";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
    {
      $message = '<div class="alert alert-danger" role="alert">
                    Invalid email address please type a valid email
                </div>';
      $form = "add";
    } elseif ($numResults > 0) {
      $message = '<div class="alert alert-danger" role="alert">
                    Account already exist
                </div>';
      $form = "add";
    } else {
      mysqli_query($connection, "insert into users(fname,lname,email,password,phone,status,username,type) values('" . $fname . "','" . $lname . "','" . $email . "','" . $password . "','" . $phone . "','" . $status . "','" . $username . "',3)");
      $message = '<div class="alert alert-success" role="alert">
      Teacher Added
                </div>';
      $id = mysqli_insert_id($connection);
      for ($i = 0; $i < count($_POST['course']); $i++) {
        mysqli_query($connection, "insert into 	t_enrolled(attempt,userid) values('" . $_POST['course'][$i] . "','" . $id . "')");
      }


      unset($form);
    }
  }
  if ($_POST['action'] == "edit") {
    $error = false;
    // print_r($_POST);
    $fname       = mysqli_real_escape_string($connection, $_POST['fname']);
    $lname       = mysqli_real_escape_string($connection, $_POST['lname']);
    $email       = mysqli_real_escape_string($connection, $_POST['email']);
    $username    = mysqli_real_escape_string($connection, $_POST['username']);
    $phone       = mysqli_real_escape_string($connection, $_POST['phone']);
    $status      = mysqli_real_escape_string($connection, $_POST['status']);
    // $access      = mysqli_real_escape_string($connection,$_POST['access']);

    $id     = mysqli_real_escape_string($connection, $_POST['id']);

    $query = "SELECT email,username FROM users where id='" . $id . "' and type=3";
    // echo $query;
    $result = mysqli_query($connection, $query);
    $results = mysqli_fetch_array($result);
    if (mysqli_num_rows($result) > 0) {
      if ($results['username'] != $username) {
        $query = "SELECT username FROM users where username='" . $username . "' and type=3";
        $result = mysqli_query($connection, $query); {
          $numResults = mysqli_num_rows($result);
          if ($numResults > 0) {
            $email = $results['email'];
            $message = '<div class="alert alert-danger" role="alert">
                                Username not available
                            </div>';
            $form = "edit";
            $error = true;
          }
        }
      } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
      {
        $message = '<div class="alert alert-danger" role="alert">
                        Invalid email address please type a valid email
                    </div>';
        $form = "add";
        $error = true;
      } elseif ($results['email'] != $email) {
        $query = "SELECT email FROM users where email='" . $email . "' and type=3";
        $result = mysqli_query($connection, $query); {
          $numResults = mysqli_num_rows($result);
          if ($numResults > 0) {
            $email = $results['email'];
            $message = '<div class="alert alert-danger" role="alert">
                                Email address already exist
                            </div>';
            $form = "edit";
            $error = true;
          }
        }
      } elseif ($error == false) {
        $query = "update users set fname='$fname', lname='$lname', username='$username',phone='$phone', email='$email', status='$status' where id='" . $id . "'";
        mysqli_query($connection, "DELETE FROM 	t_enrolled where userid='$id'");
        for ($i = 0; $i < count($_POST['course']); $i++) {
          mysqli_query($connection, "insert into 	t_enrolled(attempt,userid) values('" . $_POST['course'][$i] . "','" . $id . "')");
        }
        $result = mysqli_query($connection, $query);
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">
                        Account updated
                    </div>';
        unset($form);
      }
    } else {
      $message = '<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
      unset($form);
    }
  }
}
?>
<style>
  .dropdown-menu.show {
    overflow: overlay;
    height: 300px;
  }
</style>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Teacher</h2>
            <p class="mb-md-0">Teacher Management</p>
          </div>
        </div>
        <div class="d-flex justify-content-between align-items-end flex-wrap">
          <a href="?action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Add Teacher</a>
        </div>
      </div>
    </div>
  </div>
  <?php echo isset($message) ? $message : ''; //echo $general->myJWT();
  ?>
  <div class="row">
    <div class="col-lg-12 grid-margin stretch-card">
      <div class="card card2">
        <div class="card-body">
          <?php if (isset($form)) { ?>
            <div class="container">
              <form id="form" action="teacher.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                <div class="form-group">
                  First Name: <input type="text" name="fname" value="<?php echo isset($fname) ? $fname : ''; ?>" class="form-control form-control-lg" placeholder="First Name" required="required">
                </div>
                <div class="form-group">
                  Last Name: <input type="text" name="lname" value="<?php echo isset($lname) ? $lname : ''; ?>" class="form-control form-control-lg" placeholder="Last Name" required="required">
                </div>
                <div class="form-group">
                  Username: <input type="text" name="username" value="<?php echo isset($username) ? $username : ''; ?>" class="form-control form-control-lg" placeholder="Username" required="required">
                </div>
                <div class="form-group">
                  Email: <input type="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" class="form-control form-control-lg" placeholder="Email" required="required">
                </div>
                <?php if ($form == "add") { ?>
                  <div class="form-group">
                    Password: <input type="password" name="password" class="form-control form-control-lg" placeholder="Password" required="required">
                  </div>
                <?php } ?>
                <div class="form-group">
                  Phone: <input type="tel" name="phone" value="<?php echo isset($phone) ? $phone : ''; ?>" class="form-control form-control-lg" placeholder="Phone" required="required">
                </div>
                <div class="form-group formMult">
                  Course: <select name="course[]" id="course" multiple="multiple" class="form-control form-control-lg mt-2">
                    <?php
                    $query = "SELECT attempt.id as ID, course.title as Course,attempt.title as Attempt FROM `course`,`attempt` where course.id=attempt.courseID order by attempt.sortMe asc";
                    $result = mysqli_query($connection, $query);

                    while ($rows = mysqli_fetch_array($result)) {
                      if ($form == "edit") {
                        if ($general->isEnrolledTeacher($id, $rows['ID'])) {
                    ?>
                          <option selected="selected" value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course'] . " -> " . $rows['Attempt']; ?> Attempt</option>
                        <?php
                        } else {
                        ?>
                          <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course'] . " -> " . $rows['Attempt']; ?> Attempt</option>
                        <?php }
                      } else { ?>
                        <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course'] . " -> " . $rows['Attempt']; ?> Attempt</option>
                    <?php }
                    } ?>
                  </select>
                </div>
                <div class="form-group">
                  Account Status: <select name="status" id="status" class="form-control form-control-lg">
                    <option value="">Account Status </option>
                    <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                    <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                  </select>
                </div>
                <?php if ($form == "edit") { ?>
                  <input type="hidden" name="id" value="<?php echo $id; ?>">
                <?php } ?>
                <div class="mt-3">
                  <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                </div>
              </form>
            </div>
          <?php } else { ?>
            <div class="table-responsive">
              <table class="table table2" id="myTable">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Courses</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <!-- <th>Created</th> -->
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <?php
                  $query = "SELECT id,fname,lname,email,phone,DATE_FORMAT(created, '%d/%m/%Y') as created,username,IF(`status`=2,'Inactive','Active') AS `status`, IF(`type`=1,'Admin','User') AS `type` FROM users where `type`=3";
                  // echo $query;
                  $result = mysqli_query($connection, $query);
                  $id = 1;
                  while ($rows = mysqli_fetch_array($result)) {

                    if ($rows['id'] == 1 || userid == $rows['id']) {
                      $action = "<a href='?action=edit&id=" . $rows['id'] . "' class='badge badge-info'><i class='fa fa-pencil'></i> Edit </a>
                                <a href='?action=refresh&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to Refressh Devices for this Teacher?');\" class='badge badge-warning'><i class='fa fa-computer'></i> Refresh </a>";
                    } else {
                      $action = "<a href='?action=edit&id=" . $rows['id'] . "' class='badge badge-info'><i class='fa fa-pencil'></i> Edit </a>
                                <a href='?action=refresh&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to Refressh Devices for this Teacher?');\" class='badge badge-warning'><i class='fa fa-computer'></i> Refresh </a>
                                <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Teacher?');\" class='badge badge-danger'><i class='fa fa-trash-o'></i> Delete </a>";
                    }
                    echo "<tr>
                                    <td>" . $rows['fname'] . " " . $rows['lname'] . "</td>
                                    <td width='20%'>" . $general->myCoursesTeacher($rows['id']) . "</td>
                                    <td>" . $rows['email'] . "</td>
                                    <td>" . $rows['phone'] . "</td>
                                    <td>" . $rows['status'] . "</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                    $id++;
                  }
                  ?>

                </tbody>
              </table>
            </div>
          <?php } ?>
        </div>
      </div>
    </div>
  </div>


</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>

<style>
  .aweCheckbox {
    padding-left: 20px;
  }

  .aweCheckbox label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding: 0 20px 0 10px;
    cursor: pointer;
  }

  .aweCheckbox label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  }

  .aweCheckbox label::after {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    font-size: 11px;
    color: #555555;
  }

  .aweCheckbox input[type="checkbox"] {
    opacity: 0;
    z-index: 1;
  }

  .aweCheckbox input[type="checkbox"]:focus+label::before {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }

  .aweCheckbox input[type="checkbox"]:checked+label::after {
    font-family: "FontAwesome";
    content: "\f00c";
  }

  .aweCheckbox input[type="checkbox"]:indeterminate+label::after {
    display: block;
    content: "";
    width: 10px;
    height: 3px;
    background-color: #555555;
    border-radius: 2px;
    margin-left: -16.5px;
    margin-top: 7px;
  }

  .aweCheckbox input[type="checkbox"]:disabled+label {
    opacity: 0.65;
  }

  .aweCheckbox input[type="checkbox"]:disabled+label::before {
    background-color: #eeeeee;
    cursor: not-allowed;
  }

  .aweCheckbox.aweCheckbox-circle label::before {
    border-radius: 50%;
  }

  .aweCheckbox.aweCheckbox-inline {
    margin-top: 0;
  }

  .aweCheckbox-danger input[type="checkbox"]:checked+label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }

  .aweCheckbox-danger input[type="checkbox"]:checked+label::after {
    color: #fff;
  }

  .aweCheckbox-danger input[type="checkbox"]:indeterminate+label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }

  .aweCheckbox-danger input[type="checkbox"]:indeterminate+label::after {
    background-color: #fff;
  }

  input[type="checkbox"].styled:checked+label:after {
    font-family: \'FontAwesome\';
    content: "\f00c";
  }

  input[type="checkbox"] .styled:checked+label::before {
    color: #fff;
  }

  input[type="checkbox"] .styled:checked+label::after {
    color: #fff;
  }

  .formMult .btn-group {
    display: block;
    border: 1px solid grey;
  }

  .formMult .btn-group button {
    width: 100%;
    text-align: left;
  }
</style>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/css/bootstrap-multiselect.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/js/bootstrap-multiselect.min.js"></script>
<script>
  $(document).ready(function() {

    $("#course").multiselect({
      templates: { // Use the Awesome Bootstrap Checkbox structure
        li: '<li class="checkList"><a tabindex="0"><div class="aweCheckbox aweCheckbox-danger"><label for=""></label></div></a></li>'
      }
    });
    $(".multiselect-container div.aweCheckbox").each(function(index) {

      var id = "multiselect-" + index,
        $input = $(this).find("input");

      // Associate the label and the input
      $(this).find("label").attr("for", id);
      $input.attr("id", id);

      // Remove the input from the label wrapper
      $input.detach();

      // Place the input back in before the label
      $input.prependTo($(this));

      $(this).click(function(e) {
        // Prevents the click from bubbling up and hiding the dropdown
        e.stopPropagation();
      });

    });
  });
</script>