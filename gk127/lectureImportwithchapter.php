<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['attemptID']))
{
    $attemptID = mysqli_real_escape_string($connection, $_REQUEST['attemptID']);
    $attemptName = $general->getAttemptName($attemptID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

if (isset($_POST['action'])) {
    if ($_POST['action'] == "add") {
        if ($_FILES['lecture'] != " ") {
            $file_types = array("text/csv");
            if (is_uploaded_file($_FILES['lecture']['tmp_name'])) {
                $handle = fopen($_FILES['lecture']['tmp_name'], "r");
                $course  = mysqli_real_escape_string($connection, $_POST['course']);
                $i = 0;
                $e = 0;
                $s = 0;
                $sub = 0;
                $error = false;
                while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {
                    $i++;
                    if ($i == 1)
                        continue;

                    $topic   = mysqli_real_escape_string($connection,$data[0]);
                    $details  = mysqli_real_escape_string($connection,trim($data[1]));
                    $date  = trim($data[2]);
                    $duration  = trim($data[3]);
                    $video  = trim($data[4]);
                    $chapter  = mysqli_real_escape_string($connection,trim($data[5]));
                    if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/",$date)) {
                        
                    } else {
                        $error .= "Line # $i Invalid Date ==>>" . $date . "<br />";
                        $e++;
                        continue;
                    }
                    $s++;

                    $chapterN = $general->getSetChapter($chapter,$attemptID);
                    mysqli_query($connection, "insert into lecture(`topic`, `chapterID`, `meetingID`,`type`,`start_time`,`details`,`duration`) values('" . $topic . "', '" . $chapterN . "', '" . $video . "', '2', '" . $date . "','".$details."','".$duration."')");
                }
            }
        }


        $message = '<div class="alert alert-success" role="alert">' . ($i - 1) . ' Lecture Records Processed ' . $s . ' Added ' . $e . ' Failed</div>';

        if ($error != false) {
            $message .= '<div class="alert alert-danger" role="alert">
                    Records contain ERRORS: <br><br>' . $error . '
                </div>';
        }
        unset($form);
    }
}

?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                    <h2>Import Lectures in <?php echo $courseName; ?></h2>
                        <p class="mb-md-0">Manage your all Lectures of [<a href="attempt.php?courseID=<?php echo $general->getCourseIDbyattemptID($attemptID); ?>">BACK</a>]</p>
                        
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    
                </div>
            </div>
        </div>
    </div>
    <?php echo isset($message) ? $message : ''; //echo $general->myJWT();
    ?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">

                    <div class="container">
                        <form id="form" action="lectureImportwithchapter.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                            <div class="form-group">
                                Upload File (CSV): &nbsp;&nbsp;&nbsp; <small><a href="lecturesWChapter.csv" target="_blank">Sample CSV</a></small> <input type="file" name="lecture" class="form-control mt-2" required>
                            </div>
                            <input type="hidden" name="attemptID" value="<?php echo $attemptID; ?>">
                            <div class="mt-3">
                                <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="add">Save</button>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>


</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>