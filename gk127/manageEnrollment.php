<?php
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_GET['action'])) {
  if ($_GET['action'] == "RemoveAll") {
    $i = 1;
    foreach ($_GET['enrollment'] as $enrollment) {
      $strSQL = mysqli_query($connection, "delete from enrolled where id='" . $enrollment . "'");
      $i++;
    }

    $message = '<div class="alert alert-danger" role="alert">
        ' . $i . ' Students Disenrolled
    </div>';
  }
  if ($_GET['action'] == "deleteSingle") {
    $id       = mysqli_real_escape_string($connection, $_GET['id']);
    $eid       = mysqli_real_escape_string($connection, $_GET['eid']);
    $strSQL = mysqli_query($connection, "select id from users where id='" . $id . "'");
    if (mysqli_num_rows($strSQL) > 0) {
      $strSQL = mysqli_query($connection, "delete from enrolled where id='" . $eid . "' and userid='" . $id . "'");
      $message = '<div class="alert alert-danger" role="alert">
                    Student Disenrolled
                </div>';
    } else {
      $message = '<div class="alert alert-danger" role="alert">
                    Invalid student
                </div>';
    }
  }
}

?>
<style>
  .dropdown-menu.show {
    overflow: overlay;
    height: 300px;
  }
</style>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Enrollment</h2>
            <p class="mb-md-0">Students Enrollment</p>
          </div>
        </div>

      </div>
    </div>
  </div>
  <?php echo isset($message) ? $message : ''; //echo $general->myJWT();
  ?>
  <div class="row">
    <div class="col-lg-12 grid-margin stretch-card">
      <div class="card card2">
        <div class="card-body">


          <div class="table-responsive">
            <form action="" method="get" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
              <div class="form-group">
                <div class="input-group" style="width: 95%;margin-left: 30px;">
                  <select name="courseSearch" class="form-control form-control-lg mt-2">
                    <option value="">Select Course</option>

                    <?php
                    $query = "SELECT attempt.id as ID, course.title as Course,attempt.title as Attempt FROM `course`,`attempt` where course.id=attempt.courseID order by attempt.sortMe asc";
                    $result = mysqli_query($connection, $query);
                    $id = 1;
                    while ($rows = mysqli_fetch_array($result)) {
                      if (isset($_GET['search']) && $_GET['search'] == "search" && $_GET['courseSearch'] == $rows['ID']) {
                        ?>
                        <option selected="selected" value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']; ?> -> <?php echo $rows['Attempt']; ?></option>
                      <?php
                        } else {
                          ?>
                        <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']; ?> -> <?php echo $rows['Attempt']; ?></option>
                    <?php }
                    }
                    ?>

                  </select>
                  <div class="input-group-append" style="margin: 7px 0px -1px 1px;">
                    <button class="btn btn-sm btn-primary" type="submit" name="search" value="search">Search</button>
                  </div>
                </div>
              </div>

            </form>
            <?php
            if (isset($_GET['search']) && $_GET['search'] == "search") {
              $courseSearch       = mysqli_real_escape_string($connection, $_GET['courseSearch']);
              $query = "SELECT `E`.`id` as `eid`,`U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` ,`C`.`title` FROM `attempt` as `C`, `enrolled` as `E`,`users` as `U` where E.userid=U.id and E.attempt=$courseSearch and C.id=E.attempt and `type`=2";
              // $query = "SELECT `U`.id,fname,lname,email,phone,DATE_FORMAT(`U`.created, '%d/%m/%Y') as created,username,IF(`U`.`status`=2,'Inactive','Active') AS `status`, IF(`U`.`type`=1,'Admin','User') AS `type` FROM `enrolled` as `E`,`users` as `U` where E.userid=U.id and E.course=$courseSearch and `type`=2";
              ?>
              <form action="" method="get" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                <table class="table table2" id="myTable">
                  <thead>
                    <tr>
                      <th><input type="checkbox" name="" id="checkAll">&nbsp;&nbsp; Name</th>
                      <th>Course</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php



                      // echo $query;
                      $result = mysqli_query($connection, $query);
                      $id = 1;
                      while ($rows = mysqli_fetch_array($result)) {
                        // if($rows['id'] == 3 || $rows['id'] == 7 || $rows['id'] == 378)
                        //     continue;

                        $title = $rows['title'];
                        if ($rows['id'] == 1 || userid == $rows['id']) {
                          $action = "<a href='allowMobile.php?action=edit&id=" . $rows['id'] . "' class='badge badge-info'><i class='fa fa-pencil'></i> Edit </a>";
                        } else {
                          $action = "<a href='?courseSearch=$courseSearch&search=search&action=deleteSingle&eid=" . $rows['eid'] . "&id=" . $rows['id'] . "'' onclick=\"return confirm('Are you sure you want to Disenroll this Student from " . $rows['title'] . "?');\" class='badge badge-danger'><i class='fa fa-trash-o'></i> Disenroll </a>";
                        }
                        echo "<tr>
                                    <td><input type=\"checkbox\" name=\"enrollment[]\" value=\"" . $rows['eid'] . "\">&nbsp;&nbsp; " . $rows['fname'] . " " . $rows['lname'] . "</td>
                                    <td width='20%'>" . $rows['title'] . "</td>
                                    <td>" . $rows['email'] . "</td>
                                    <td>" . $rows['phone'] . "</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                        $id++;
                      }
                      ?>

                  </tbody>



                </table>
                <input type="hidden" name="courseSearch" value="<?php echo $courseSearch; ?>">
                <input type="hidden" name="search" value="search">
                <input type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" onclick="return confirm('Are you sure you want to Disenroll all Students from <?php echo $title; ?>?');" value="RemoveAll">
              </form>
            <?php } ?>
          </div>

        </div>
      </div>
    </div>
  </div>


</div>
<!-- /page content -->
<?php
$footerJS = '
<link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" />

<style>

.aweCheckbox {
    padding-left: 20px;
  }
  .aweCheckbox label {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    padding: 0 20px 0 10px;
    cursor: pointer;
  }
  .aweCheckbox label::before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 17px;
    height: 17px;
    left: 0;
    margin-left: -20px;
    border: 1px solid #cccccc;
    border-radius: 3px;
    background-color: #fff;
    -webkit-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    -o-transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
    transition: border 0.15s ease-in-out, color 0.15s ease-in-out;
  }
  .aweCheckbox label::after {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    left: 0;
    top: 0;
    margin-left: -20px;
    padding-left: 3px;
    padding-top: 1px;
    font-size: 11px;
    color: #555555;
  }
  .aweCheckbox input[type="checkbox"] {
    opacity: 0;
    z-index: 1;
  }
  .aweCheckbox input[type="checkbox"]:focus + label::before {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
  }
  .aweCheckbox input[type="checkbox"]:checked + label::after {
    font-family: "FontAwesome";
    content: "\f00c";
  }
  .aweCheckbox input[type="checkbox"]:indeterminate + label::after {
    display: block;
    content: "";
    width: 10px;
    height: 3px;
    background-color: #555555;
    border-radius: 2px;
    margin-left: -16.5px;
    margin-top: 7px;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label {
    opacity: 0.65;
  }
  .aweCheckbox input[type="checkbox"]:disabled + label::before {
    background-color: #eeeeee;
    cursor: not-allowed;
  }
  .aweCheckbox.aweCheckbox-circle label::before {
    border-radius: 50%;
  }
  .aweCheckbox.aweCheckbox-inline {
    margin-top: 0;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:checked + label::after {
    color: #fff;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::before {
    background-color: #d9534f;
    border-color: #d9534f;
  }
  .aweCheckbox-danger input[type="checkbox"]:indeterminate + label::after {
    background-color: #fff;
  }
  input[type="checkbox"].styled:checked + label:after {
    font-family: \'FontAwesome\';
    content: "\f00c";
  }
  input[type="checkbox"] .styled:checked + label::before {
    color: #fff;
  }
  input[type="checkbox"] .styled:checked + label::after {
    color: #fff;
  }
  .formMult .btn-group{
    display: block;
    border: 1px solid grey;
}
.formMult .btn-group button{
    width: 100%;
    text-align: left;
}
</style>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/css/bootstrap-multiselect.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/js/bootstrap-multiselect.min.js"></script>






<script src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    $("#course").multiselect({
      templates: { // Use the Awesome Bootstrap Checkbox structure
        li: \'<li class="checkList"><a tabindex="0"><div class="aweCheckbox aweCheckbox-danger"><label for=""></label></div></a></li>\'
      }
    });
    $(".multiselect-container div.aweCheckbox").each(function(index) {
  
      var id = "multiselect-" + index,
        $input = $(this).find("input");
  
      // Associate the label and the input
      $(this).find("label").attr("for", id);
      $input.attr("id", id);
  
      // Remove the input from the label wrapper
      $input.detach();
  
      // Place the input back in before the label
      $input.prependTo($(this));
  
      $(this).click(function(e) {
        // Prevents the click from bubbling up and hiding the dropdown
        e.stopPropagation();
      });
  
    });
  });

  $(document).ready( function () {
    $("#myTable").DataTable({
        pageLength : 100,
    lengthMenu: [[250, 500, 1000, -1], [250, 500, 1000, \'All\']],
    dom: \'Bfrtip\',
        buttons: [
            \'copy\', \'csv\', \'excel\', \'pdf\'
        ]
    });
    $("#myTable td").css("white-space","initial");
} );
</script>
<script>
$("#checkAll").click(function(){
    $("input:checkbox").not(this).prop("checked", this.checked);
});
</script>
';
include("includes/footer.php");
?>