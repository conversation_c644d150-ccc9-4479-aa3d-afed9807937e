<?php 
include_once('includes/config.php');
include_once('includes/session.php');
include("includes/generalFunctions.class.php");

header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

$general = new generalFunctions($connection);

if($general->showToken(userid) == $_SESSION['token'])
{
    $data['return'] = 1;
}
else
{
    $data['return'] = 2;
}
echo json_encode($data); 
exit;

?>