<?php
include('includes/config.php');
$error = false;
if (isset($_GET['action'])) {
    if ($_GET['action'] == "reset") {
        $encrypt = mysqli_real_escape_string($connection, $_GET['encrypt']);
        $query = "SELECT id FROM users where md5(90*13+id)='" . $encrypt . "'";
        $result = mysqli_query($connection, $query);
        $results = mysqli_fetch_array($result);
        if (mysqli_num_rows($result) > 0) {
            $reset = true;
        } else {
            $message = alert("danger","Invalid key please try again.");
        }
    }
} 
if (isset($_POST['action'])) {
    

    if ($_POST['action'] == "password" && $error == false) {
        
        $email      = mysqli_real_escape_string($connection, $_POST['email']);
        
            $query = "SELECT * FROM users where email = '" . $email . "'";
            $result = mysqli_query($connection, $query);


            if (mysqli_num_rows($result) > 0) {
                $results = mysqli_fetch_array($result);
                $encrypt = md5(90 * 13 + $results['id']);
                $to = $email;
                $subject = "Forget Password";
                $from = webMaster;
                $body = 'Hello ' . $results['fname'] . ', <br/> <br/>A Password reset request received please follow instruction below.<br>If you didn\'t requested please ignore this email. <br><br>Click here to reset your password ' . siteurl . '/forgot.php?encrypt=' . $encrypt . '&action=reset   <br/> <br/>--<br>' . siteurl;
                $headers = "From: " . strip_tags($from) . "\r\n";
                $headers .= "Reply-To: " . strip_tags($from) . "\r\n";
                $headers .= "CC: " . webMaster . "\r\n";
                $headers .= "MIME-Version: 1.0\r\n";
                $headers .= "Content-Type: text/html; charset=ISO-8859-1\r\n";
                // echo $body;
                if(host == "live")
                    mail($to,$subject,$body,$headers);   
                $message = alert("success","Password reset email sent!!");             
                
            } else {
                $message = alert("danger","Invalid username/email address please type a valid email!!"); 
            }
        
    } elseif ($_POST['action'] == "update"  && $error == false) {
        $encrypt      = mysqli_real_escape_string($connection, $_POST['encrypt']);
        $password     = password_hash(mysqli_real_escape_string($connection, $_POST['password']), PASSWORD_DEFAULT);
        $query = "SELECT id FROM users where md5(90*13+id)='" . $encrypt . "'";
        $result = mysqli_query($connection, $query);
        if($_POST['password'] != $_POST['password2'])
        {
            $message = alert("danger","Your password don't match."); 
        }
        elseif (mysqli_num_rows($result) > 0) {
            $results = mysqli_fetch_array($result);
            $query = "update users set password='" . $password . "' where id='" . $results['id'] . "'";
            //            echo $query;
            mysqli_query($connection, $query);
            $message = alert("success","Your password changed sucessfully <a href='" . siteurl . "/login.php'>click here</a> to login"); 
        } else {
            $message = alert("danger","Invalid key please try again. <a href='" . siteurl . "/reset.php'>Forget Password?</a>"); 
        }
    }
}

?>





<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Forget Password | <?php echo sitename; ?></title>
    <!-- plugins:css -->
    <link rel="stylesheet" href="assets/vendors/mdi/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="assets/vendors/base/vendor.bundle.base.css">
    <!-- endinject -->
    <!-- plugin css for this page -->
    <!-- End plugin css for this page -->
    <!-- inject:css -->
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- endinject -->
    <link rel="shortcut icon" href="assets/images/favicon.png" />
    
</head>

<body>
    <div class="container-scroller">
        <div class="container-fluid page-body-wrapper full-page-wrapper">
            <div class="content-wrapper d-flex align-items-center auth px-0">
                <div class="row w-100 mx-0">
                    <div class="col-lg-4 mx-auto">
                        <div class="auth-form-light text-left py-5 px-4 px-sm-5">
                            <div class="brand-logo">
                                <img src="<?php echo websiteLogo; ?>" alt="logo">
                            </div>
                            <?php echo isset($message) ? $message : ''; ?>
                            
                            <form class="pt-3" action="" method="post">
                                <?php
                                if (isset($reset)) {
                                    ?>
                                    <h4>Reset Password</h4>
                                    <div class="form-group">
                                        <input type="password" name="password" class="form-control form-control-lg" id="InputPassword1" placeholder="Enter new password" required="required">
                                    </div>
                                    <div class="form-group">
                                        <input type="password" name="password2" class="form-control form-control-lg" id="InputPassword1" placeholder="Re-type new password" required="required">
                                    </div>
                                    <div class="g-recaptcha" data-sitekey="<?php echo recaptcha_SITE_KEY; ?>"></div>
                                    <div class="mt-3">
                                    <input name="encrypt" type="hidden" value="<?php echo $encrypt; ?>" />
                                        <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="update">CHANGE PASSWORD</button>
                                    </div>
                                <?php
                                } else {
                                    ?>
                                    <h4>Forget Password</h4>
                                    <div class="form-group">
                                        <input type="text" name="email" class="form-control form-control-lg" id="InputEmail1" placeholder="Email/Username" required="required">
                                    </div>
                                    
                                    <div class="mt-3">
                                        <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="password">RESET PASSWORD</button>
                                    </div>
                                <?php
                                }
                                ?>

                                <div class="my-2 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                    <label class="form-check-label text-muted">
                      <!-- <input type="checkbox" class="form-check-input">
                      Keep me signed in -->
                    </label>
                  </div>
                                    <a href="login.php" class="auth-link text-black">Login</a>
                                </div>
                                <div class="text-center mt-4 font-weight-light">
                                    <!-- Don't have an account? <a href="signup.php" class="text-primary">Create</a> -->
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <!-- content-wrapper ends -->
        </div>
        <!-- page-body-wrapper ends -->
    </div>
    <!-- container-scroller -->
    <!-- plugins:js -->
    <script src="assets/vendors/base/vendor.bundle.base.js"></script>
    <!-- endinject -->
    <!-- inject:js -->
    <script src="assets/js/off-canvas.js"></script>
    <script src="assets/js/hoverable-collapse.js"></script>
    <script src="assets/js/template.js"></script>
    <!-- endinject -->
</body>

</html>