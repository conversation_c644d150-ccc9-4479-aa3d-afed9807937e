<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['id'])) {
    $id = mysqli_real_escape_string($connection, $_REQUEST['id']);
    $lectureTopic = $general->getLectureTopic($id);
} else {
    echo notify("Some error occured please try again.");
    exit;
}
$courseID = $general->getCourseIDFeomLectureID($id);
if (!$general->isEnrolled(userid, $courseID)) {
    echo notify("Sorry you are not enrolled in this course.");
    exit;
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2><?php echo $lectureTopic; ?></h2>

                    </div>
                </div>
                <div class="align-right"><h3><a href="recording.php?courseID=<?php echo $courseID; ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>

            </div>
        </div>
    </div>
<div id="loading"><img src="assets/ajax-loader.gif?v=1" /></div>

    <?php
    $query = "SELECT * FROM handout where lectureid=$id";
    // echo  $query;
    $result = mysqli_query($connection, $query);
    if (mysqli_num_rows($result) > 0) {
        $iie = 1;
        $jsees = "";
        while ($rows = mysqli_fetch_array($result)) {
            $hTitle = $rows['title'];
            $jsees .= "
                            var url_$iie = 'assets/handouts/" . $rows['file'] . "';
                            loadPDFJS(url_$iie,'pdf-container_$iie');
                        ";
            ?>
            <div class="col-md-12 grid-margin mb-4 mt-4">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="d-flex align-items-end flex-wrap">
                        <div class="mr-md-3 mr-xl-5">
                            <h4 class="mb-4"><?php echo $hTitle; ?></h4>
                                <div id="pdf-container_<?php echo $iie; ?>">
                            </div>
                        </div>

                    </div>
                </div>
        <?php 
    $iie++;    
    }
        } ?>


            </div>
            <!-- /page content -->
            <style>
                .makepointer {
                    cursor: pointer;
                }
            </style>
            <?php
            $footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
            include("includes/footer.php");
            ?>

            <script type="text/javascript" src="assets/js/pdf.js?v=1"></script>

            <script type="text/javascript">
                <?php echo $jsees; ?>

                function loadPDFJS(pageUrl, container) {

                    PDFJS.workerSrc = 'https://mozilla.github.io/pdf.js/build/pdf.worker.js';
                    var currentPage = 1;
                    var pages = [];
                    var globalPdf = null;
                    var container = document.getElementById(container);

                    function renderPage(page) {
                        var canvas = document.createElement('canvas');
                        var viewport = page.getViewport(1000 / page.getViewport(1.0).width);
                        container.appendChild(canvas);
                        var context = canvas.getContext('2d');
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;
                        var renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };
                        page.render(renderContext).then(function() {
                            if (currentPage < globalPdf.numPages) {
                                pages[currentPage] = canvas;
                                currentPage++;
                                globalPdf.getPage(currentPage).then(renderPage);
                            } else {
                                $("#loading").hide();
                                // Callback function here, which will trigger when all pages are loaded
                            }
                        });
                    }
                    PDFJS.getDocument(pageUrl).then(function(pdf) {
                        if (!globalPdf) {
                            globalPdf = pdf;
                        }
                        pdf.getPage(currentPage).then(renderPage);
                    });
                }
            </script>