<?php 
include_once('includes/config.php');
include_once('includes/session.php');
include("includes/generalFunctions.class.php");
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

$general = new generalFunctions($connection);


if(isset($_GET['action']))
{          
    if($_GET['action']=="message")
    {
        $message = mysqli_real_escape_string($connection,$_GET['message']);
        $lectureID = mysqli_real_escape_string($connection,$_GET['lectureID']);
        $query = "INSERT into  `chat`(`userid`, `lectureID`, `message`) values('".userid."', '$lectureID', '$message')";
        $strSQL = mysqli_query($connection,  $query);
        $data['return'] = true;
        echo json_encode($data); 
        exit;
    }
}


?>