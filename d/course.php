<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['levelID']))
{
    $levelID = mysqli_real_escape_string($connection,$_REQUEST['levelID']);
    $className = $general->getLevelName($levelID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

?>

<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
          <h2>Papers in <?php echo $className; ?></h2>
                        
          </div>
        </div>
        <div class="align-right"><h3><a href="level.php?classID=<?php echo $general->getClassIDbylevelID($levelID); ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>

      </div>
    </div>
  </div>
  <div class="container">
  <div class="row">
  <?php
    $query = "SELECT * FROM course where levelID=$levelID and status=1 order by sortMe";
    $result = mysqli_query($connection, $query);
    $id = 1;
    while ($rows = mysqli_fetch_array($result)) {
    
  ?>

<div class="col-md-6 col-lg-4 column">
        <div class="cardNew gr-2">
        <a href='attempt.php?courseID=<?php echo $rows['id']; ?>' class="nav-link">
          <div class="txt">
            <h1> <?php echo $rows['title']; ?> </h1>
            <p><?php echo nl2br($rows['desc']); ?></p>
          </div>
        </a>
          <div class="ico-card">
            <i class="fa fa-video-camera"></i>
          </div>
        </div>
      </div>



    <?php }  ?>
  
   
  
</div>
</div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>