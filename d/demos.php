<?php
include("includes/header.php");
?>
<style>
    p {
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        line-height: 2rem;
    }
    .dtrxt{
        font-size: 20px;
        color: #625f5f;
    }
</style>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Demo Videos</h2>

                    </div>
                </div>


            </div>
        </div>
    </div>

    <div class="row">

        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <table class="border">
                        <thead>
                            <tr class="border">
                                <th class="text-center dtrxt p-3">CA Demo Videos</th>
                                <th class="text-center dtrxt p-3">ACCA Demo Videos</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border">
                                <td class="border" style="width: 50%; vertical-align: baseline;">
                                    <div class="row text-center">
                                        <?php
                                        $query = "SELECT * FROM `demos` where `cat`=1";
                                        // echo  $query;
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {
                                            ?>

                                            <div class="col-12 p-4">
                                                <div class="mb-2"><?php echo $rows['title']; ?></div>
                                                <a class="various fancybox fancybox.iframe" href="https://www.youtube.com/embed/<?php echo $rows['video']; ?>?autoplay=1" title="<?php echo $rows['title']; ?>">
                                                    <img style="width:100%;" src="https://img.youtube.com/vi/<?php echo $rows['video']; ?>/0.jpg" alt="<?php echo $rows['title']; ?>">
                                                </a>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </td>
                                <td class="border" style="width: 50%; vertical-align: baseline;">
                                    <div class="row text-center">
                                        <?php
                                        $query = "SELECT * FROM `demos` where `cat`=2";
                                        // echo  $query;
                                        $result = mysqli_query($connection, $query);
                                        $id = 1;
                                        while ($rows = mysqli_fetch_array($result)) {
                                            ?>

                                            <div class="col-12 p-4">
                                                <div class="mb-2"><?php echo $rows['title']; ?></div>
                                                <a class="various fancybox fancybox.iframe" href="https://www.youtube.com/embed/<?php echo $rows['video']; ?>?autoplay=1">
                                                    <img style="width:100%;" src="https://img.youtube.com/vi/<?php echo $rows['video']; ?>/0.jpg" alt="<?php echo $rows['title']; ?>">
                                                </a>
                                            </div>

                                            <?php ?>
                                        <?php } ?>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- /page content -->
<?php

$footerJS = "

<script type='text/javascript' src='assets/js/jquery.fancybox.js?v=2.1.5'></script>
<link rel='stylesheet' type='text/css' href='assets/css/jquery.fancybox.css?v=2.1.5' media='screen' />
<script>
  $('.fancybox').fancybox({
    openEffect	: 'elastic',
    closeEffect	: 'elastic',
    
    helpers : {
        title : {
            type : 'inside' 
        }    
    },
    beforeLoad: function() {
        this.title = this.title+`  <a class='aalink' href='https://www.youtube.com/channel/UCQf6Q26vXPWxdE9xIEVlq4Q?sub_confirmation=1&feature=subscribe-embed-click' target='_blank'> Subscribe Now</a>`;
       }
});
</script>";
include("includes/footer.php");
?>