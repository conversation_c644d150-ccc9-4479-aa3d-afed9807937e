<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['chapterID'])) {
  $chapterID = mysqli_real_escape_string($connection, $_REQUEST['chapterID']);
  $courseName = $general->getChapterName($chapterID);
} else {
  echo notify("Some error occured please try again.");
  exit;
}
function getLectureParts($lid)
{
  global $connection;
  $query = "SELECT * FROM `lecture` where `partof`=$lid and `type`=2 order by sortMe";
  // echo  $query;
  $result = mysqli_query($connection, $query);

  if (mysqli_num_rows($result) > 0)
    return $result;
  else
    return false;
}
?>
<style>
  .styledMain {
    font-size: 15px;
  }
  .styled {
    margin-top: -3px;
    margin-left: 10px;
    font-size: 15px;
  }
</style>
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          
        </div>
        <div class="align-right">
          <h3><a href="chapters_new.php?attemptID=<?php echo $general->getattemptIDbyChapterID($chapterID); ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3>
        </div>

      </div>
    </div>
  </div>

  <div class="row">
    
    <div class="col-md-12 col-lg-12 column mb-2">
      
      <ul class="list-group">
        <li class="list-group-item">
          <div class="mr-md-3 mr-xl-5">
            <h2><?php echo $courseName; ?></h2>
          </div>
        </li>
        <?php

        // $query = "SELECT * FROM lecture where chapterID=$chapterID and type=2 ORDER BY `start_time` DESC";
        $query = "SELECT * FROM `lecture` where `chapterID`=$chapterID and `partof`= 0 and `type`=2 order by sortMe";
        // echo  $query;
        $result = mysqli_query($connection, $query);
        $id = 1;
        while ($rows = mysqli_fetch_array($result)) {
          $duration = false;
          $parts = getLectureParts($rows['id']);
          if ($parts == false) {
            if($rows['duration']>0)
              $duration = "<i class=\"mdi mdi-clock menu-icon clockSize\"></i> Total Hours: ". gmdate("H:i:s", $rows['duration']);
        ?>
            <li class="list-group-item">
            <div class="d-flex ">
              <i class="fa fa-play"></i>
              
                <p class="styled d-flex"> <a href='recPlay.php?id=<?php echo $rows['id']; ?>'><?php echo $rows['topic']; ?></a> <span class="dot <?php echo $general->videoWatchedSignalColor($rows['id'],userid); ?>"></span> &nbsp;&nbsp;&nbsp;&nbsp;<?php echo $duration; ?></p>
              
            </div>
              <p> <?php echo nl2br($rows['details']); ?> </p>
              <p class="mt-2 text-muted">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>
              <?php if($general->isHandouts($rows['id'])){ ?>
                <p>
                
          <a href='handouts.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;">Total Handouts (<?php echo $general->getNumberOfHandoutInlecture($rows['id']); ?>)</a>
        </p>
        <?php } ?>
        <?php if($general->isTest($rows['id'])){ ?>
          <p>
          <a href='test.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;"> Class Test/Quiz</a>
          </p>
          <?php } ?>
            </li>
            
          <?php
          } else {
          ?>
            <li class="list-group-item">
              <p class="styledMain"> <?php echo $rows['topic']; ?> </p>
              <p class="ml-2"> <?php echo nl2br($rows['details']); ?> </p>
              <p class="mt-2 ml-2 text-muted">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>

              <ul>
                <li class="list-unstyled">
                  <div class="d-flex">
                    <i class="fa fa-play"></i>
                    <a href='recPlay.php?id=<?php echo $rows['id']; ?>'>
                      <p class="styled"><?php echo $rows['part']; ?> </p>
                    </a>
                  </div>

                </li>
                <?php
                while ($rowsParts = mysqli_fetch_array($parts)) {
                  if($rowsParts['duration']>0)
                    $duration = "<i class=\"mdi mdi-clock menu-icon\"></i> Total Hours: ". gmdate("H:i:s", $rowsParts['duration']);
                ?>
                  <li class="list-unstyled">
                    <div class="d-flex">
                      <i class="fa fa-play"></i>
                      
                        <p class="styled"><a href='recPlay.php?id=<?php echo $rowsParts['id']; ?>'><?php echo $rowsParts['part']; ?></a> <span class="dot <?php echo $general->videoWatchedSignalColor($rowsParts['id'],userid); ?>"></span> &nbsp;&nbsp;&nbsp;&nbsp;<?php echo $duration; ?></p>
                      
                    </div>

                  </li>
                <?php
                }
                ?>
              </ul>
            </li>
          <?php

          }
          ?>










        <?php }  ?>
      </ul>

    </div>
  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
  .dot{
    padding: 10px !important;
    margin-left: 20px !important;
  }
  .clockSize{
    font-size: 20px;
    margin-left: 40px;
    margin-right: 10px;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
exit;
?>

<a href='recPlay.php?id=<?php echo $rows['id']; ?>' class="nav-link">


  <p class="mt-2 text-muted card-text">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>
  <!-- <p class="mt-2 text-muted card-text">Duration: <?php echo date('H:i', mktime(0, $rows['duration'])); ?></p> -->
  </div>
</a>
<div class="row" style="left:15px;width: 100%;bottom: 45px;position: absolute;font-size:.9rem">
  <div class="col-6">
    <?php if ($general->isHandouts($rows['id'])) { ?>
      <a href='handouts.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;">Total Handouts (<?php echo $general->getNumberOfHandoutInlecture($rows['id']); ?>)</a>
    <?php } ?>
  </div>
  <div class="col-6">
    <?php if ($general->isTest($rows['id'])) { ?>
      <a href='test.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;"> Class Test/Quiz</a>
    <?php } ?>
  </div>
</div>
<div class="ico-card">
  <i class="fa fa-video-camera"></i>
</div>
</div>