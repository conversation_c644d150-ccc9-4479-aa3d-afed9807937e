<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);


?>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
          <h2>Welcome to KnS Institute's Online Portal</h2>
                        
          </div>
        </div>

      </div>
    </div>
  </div>


  <div class="row">
    
    <div class="col-md-12 col-lg-12 column mb-2">
      
      <ul class="list-group">
        <li class="list-group-item">
          <div class="mr-md-3 mr-xl-5">
            <h2>My Courses (<?php echo fname." ".lname; ?> - <?php echo email; ?>)</h2>
          </div>
        </li>
        <?php

        $query = "SELECT * FROM `enrolled`,`attempt` where `attempt`.`id`=`enrolled`.`attempt` and `enrolled`.`userid`='".userid."' order by `attempt`.`sortMe`";
        $result = mysqli_query($connection, $query);
        if(mysqli_num_rows($result)>0){
        while ($rows = mysqli_fetch_array($result)) {
        ?>
           
            <li class="list-group-item p-4">
            <div class="d-flex">
            <i class="fa fa-video-camera"></i>
              <a href='chapters_new.php?attemptID=<?php echo $rows['id']; ?>'>
                <p class="styled bigFont"> <?php echo $general->getCourseName($rows['courseID'])." ".$rows['title'] ?></p>
              </a>
            </div>
            
              <p> <?php echo nl2br($rows['desc']); ?> </p>
              
            </li>  
          <?php

          } }else{ 
            echo notify("You are not enrolled in any course.");
         }
          ?>
      </ul>

    </div>
  </div>

  
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
  .styledMain {
    font-size: 15px;
  }
  .styled {
    margin-top: -3px;
    margin-left: 10px;
    font-size: 15px;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>