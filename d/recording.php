<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['courseID']))
{
    $courseID = mysqli_real_escape_string($connection,$_REQUEST['courseID']);
    $courseName = $general->getCourseName($courseID);
}
else
{
    echo notify("Some error occured please try again.");
    exit;
}

?>

<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Recorded Lectures in <?php echo $courseName; ?></h2>
            
          </div>
        </div>
        <div class="align-right"><h3><a href="lecture.php?courseID=<?php echo $courseID; ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>
        <?php
        
        if(!$general->isEnrolled(userid,$courseID)){
          echo notify("Sorry you are not enrolled in this course.");
          exit;
        }
        
        ?>
      </div>
    </div>
  </div>

  <div class="row">
  <?php
    // $query = "SELECT * FROM lecture where courseID=$courseID and type=2 ORDER BY `start_time` DESC";
    $query = "SELECT * FROM lecture where courseID=$courseID and type=2";
    // echo  $query;
    $result = mysqli_query($connection, $query);
    $id = 1;
    while ($rows = mysqli_fetch_array($result)) {
      
  ?>


<div class="col-md-4 grid-margin">
      <div class="card d-flex align-items-center">
        <div class="card-body" style="height: 199px;">
          <a href='recPlay.php?id=<?php echo $rows['id']; ?>' style="text-decoration: none;">
            <div class="d-flex flex-row align-items-center" style="height: 120px;">
              <i class="mdi mdi mdi-message-video text-youtube icon-md"></i>
              <div class="ml-3">
                <h6 class="text-youtube"><?php echo $rows['topic']; ?></h6>
                <p class="mt-2 text-muted card-text">Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>
                
              </div>
            </div>
          </a>
          
          <div class="pl-4 ml-4">
          <?php if($general->isHandouts($rows['id'])){ ?>
          <div class="col-md-12 mb-2">
          <a href='handouts.php?id=<?php echo $rows['id']; ?>' style="text-decoration: none;color: #fff;"><i class='mdi mdi-file-multiple'></i> Handouts</a>
          </div>
          <?php } ?>


          <?php if($general->isTest($rows['id'])){ ?>
          <div class="col-md-12 mb-2">
          <a href='test.php?id=<?php echo $rows['id']; ?>' style="text-decoration: none;color: #fff;"><i class='mdi mdi-content-paste'></i> Class Test/Quiz</a>
          </div>
          <?php } ?>
          
        </div>
    
        </div>
      </div>
      
    </div>



    <?php }  ?>
  
</div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>