<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['chapterID'])) {
  $chapterID = mysqli_real_escape_string($connection, $_REQUEST['chapterID']);
  $courseName = $general->getChapterName($chapterID);
} else {
  echo notify("Some error occured please try again.");
  exit;
}
function getLectureParts($lid)
{
  global $connection;
  $query = "SELECT * FROM `lecture` where `partof`=$lid and `type`=2 order by sortMe";
  // echo  $query;
  $result = mysqli_query($connection, $query);

  if (mysqli_num_rows($result) > 0)
    return $result;
  else
    return false;
}
?>
<style>
  .styledMain {
    font-size: 15px;
  }
  .styled {
    margin-top: -3px;
    margin-left: 10px;
    font-size: 15px;
  }
</style>
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Recorded Lectures in <?php echo $courseName; ?></h2>

          </div>
        </div>
        <div class="align-right">
          <h3><a href="chapters.php?attemptID=<?php echo $general->getattemptIDbyChapterID($chapterID); ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3>
        </div>

      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12 col-lg-12 column mb-2">
      <ul class="list-group">

        <?php

        // $query = "SELECT * FROM lecture where chapterID=$chapterID and type=2 ORDER BY `start_time` DESC";
        $query = "SELECT * FROM `lecture` where `chapterID`=$chapterID and `partof`= 0 and `type`=2 order by sortMe";
        // echo  $query;
        $result = mysqli_query($connection, $query);
        $id = 1;
        while ($rows = mysqli_fetch_array($result)) {
          $parts = getLectureParts($rows['id']);
          if ($parts == false) {
        ?>
            <li class="list-group-item">
            <div class="d-flex">
              <i class="fa fa-play"></i>
              <a href='recPlay.php?id=<?php echo $rows['id']; ?>'>
                <p class="styled"> <?php echo $rows['topic']; ?> </p>
              </a>
            </div>
              <p> <?php echo nl2br($rows['details']); ?> </p>
              <p class="mt-2 text-muted">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>
            </li>
          <?php
          } else {
          ?>
            <li class="list-group-item">
              <p class="styledMain"> <?php echo $rows['topic']; ?> </p>
              <p class="ml-2"> <?php echo nl2br($rows['details']); ?> </p>
              <p class="mt-2 ml-2 text-muted">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>

              <ul>
                <li class="list-unstyled">
                  <div class="d-flex">
                    <i class="fa fa-play"></i>
                    <a href='recPlay.php?id=<?php echo $rows['id']; ?>'>
                      <p class="styled"><?php echo $rows['part']; ?> </p>
                    </a>
                  </div>

                </li>
                <?php
                while ($rowsParts = mysqli_fetch_array($parts)) {
                ?>
                  <li class="list-unstyled">
                    <div class="d-flex">
                      <i class="fa fa-play"></i>
                      <a href='recPlay.php?id=<?php echo $rowsParts['id']; ?>'>
                        <p class="styled"><?php echo $rowsParts['part']; ?> </p>
                      </a>
                    </div>

                  </li>
                <?php
                }
                ?>
              </ul>
            </li>
          <?php

          }
          ?>










        <?php }  ?>
      </ul>

    </div>
  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
exit;
?>

<a href='recPlay.php?id=<?php echo $rows['id']; ?>' class="nav-link">


  <p class="mt-2 text-muted card-text">Lecture Date: <?php echo $general->dateToRead($rows['start_time']); ?></p>
  <!-- <p class="mt-2 text-muted card-text">Duration: <?php echo date('H:i', mktime(0, $rows['duration'])); ?></p> -->
  </div>
</a>
<div class="row" style="left:15px;width: 100%;bottom: 45px;position: absolute;font-size:.9rem">
  <div class="col-6">
    <?php if ($general->isHandouts($rows['id'])) { ?>
      <a href='handouts.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;">Total Handouts (<?php echo $general->getNumberOfHandoutInlecture($rows['id']); ?>)</a>
    <?php } ?>
  </div>
  <div class="col-6">
    <?php if ($general->isTest($rows['id'])) { ?>
      <a href='test.php?id=<?php echo $rows['id']; ?>' class="nav-link" style="font-size:.9rem;"> Class Test/Quiz</a>
    <?php } ?>
  </div>
</div>
<div class="ico-card">
  <i class="fa fa-video-camera"></i>
</div>
</div>