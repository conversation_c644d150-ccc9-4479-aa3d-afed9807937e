<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");
if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
    $message = $_SESSION['msg'];
    unset($_SESSION['msg']);
}
$general = new generalFunctions($connection);
$page = "Message";
if (isset($_GET['action'])) {
    if ($_GET['action'] == "add") {
        $form = "add";
        $whatPage = "Add ";
    }
    if ($_GET['action'] == "view") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $form = "view";
        $whatPage = "View ";
    }
    if ($_GET['action'] == "reopen") {
        $id       =     mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "UPDATE `complains` set `status`=1 where id='" . $id . "' and `uid`='" . userid . "'");
        $_SESSION['msg'] = '<div class="alert alert-success" role="alert">Complain Re-Opened</div>';
        echo '<meta http-equiv="refresh" content="0;url=contactus.php?action=view&id=' . $id . '" />';
        exit;
    }
}
if (isset($_POST['action'])) {

    if ($_POST['action'] == "add") {
        $title = mysqli_real_escape_string($connection, $_POST['title']);
        $complain = mysqli_real_escape_string($connection, $_POST['complain']);
        $attemptID = mysqli_real_escape_string($connection, $_POST['attemptID']);

        $query = "INSERT INTO `complains`(`uid`, `title`,`attemptID`) VALUES ('" . userid . "', '$title','$attemptID')";
        mysqli_query($connection, $query);
        $cid = mysqli_insert_id($connection);

        $query = "INSERT INTO `complainText`(`uid`, `cid`, `complain`, `isReadA`) VALUES ('" . userid . "','$cid', '$complain', 1)";
        mysqli_query($connection, $query);

        $_SESSION['msg'] = '<div class="alert alert-success" role="alert">Complain Created</div>';
        echo '<meta http-equiv="refresh" content="0;url=contactus.php?action=view&id=' . $cid . '" />';
        exit;
        unset($form);
    } elseif ($_POST['action'] == "reply") {
        $id = mysqli_real_escape_string($connection, $_POST['id']);
        $complain = mysqli_real_escape_string($connection, $_POST['complain']);

        $query = "INSERT INTO `complainText`(`uid`, `cid`, `complain`, `isReadA`) VALUES ('" . userid . "','$id', '$complain', 1)";
        mysqli_query($connection, $query);
        if($_POST['resolved'] == 1)
        {
            $query = "UPDATE `complains` SET `status`='2' WHERE `id`='$id' and `uid` = '" . userid . "'";
            mysqli_query($connection, $query);
        }
        $_SESSION['msg'] = '<div class="alert alert-success" role="alert">Messsage Replied</div>';
        
        echo '<meta http-equiv="refresh" content="0;url=contactus.php?action=view&id=' . $id . '" />';
        exit;
        unset($form);
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        unset($form);
    }
}

?>
<style>
    

    .table td img {
        width: auto;
        height: auto;
        border-radius: 0;
    }
    .card{
        background-color: white !important;
    }
    .table{
        color: #000 !important; 
    }
    .table th, .table td {
    padding: 1.25rem 0.9375rem;
    vertical-align: top;
    border-top: 1px solid #000;
}
    
</style>
<!-- page content -->
<div class="content-wrapper">
    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2><?php echo $whatPage . $page; ?></h2>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                    <a href="?action=add" class="btn btn-primary mt-2 mt-xl-0"><i class="mdi mdi-plus"></i> Send <?php echo $page; ?></a>
                </div>

            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 grid-margin">
            <div class="card">
                <div class="card-body">

                    <?php echo isset($message) ? $message : ''; ?>

                    <?php if (isset($form) && ($form == "edit" || $form == "add")) { ?>
                        <form id="form" action="" method="post" enctype="multipart/form-data" data-parsley-validate>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-12 ">Course * </label>
                                        <div class="col-sm-12">
                                            <select name="attemptID" id="attemptID" class="form-control form-control-lg mt-2" required>
                                                <option value="">Select Course</option>
                                                <?php
                                                $query = "SELECT attempt.id as ID, course.title as Course,attempt.title as Attempt FROM `course`,`attempt` where course.id=attempt.courseID order by attempt.sortMe asc";
                                                $result = mysqli_query($connection, $query);
                                                while ($rows = mysqli_fetch_array($result)) {
                                                    
                                                    if($general->isEnrolled(userid,$rows['ID']) == false)
                                                        continue;
                                                    ?>
                                                    <option value="<?php echo $rows['ID']; ?>"><?php echo $rows['Course']; ?> -> <?php echo $rows['Attempt']; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-12 ">Title *</label>
                                        <div class="col-sm-12">
                                            <input type="text" value="<?php echo isset($title) ? $title : ''; ?>" name="title" class="form-control" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="col-sm-12 ">Complain *</label>
                                        <div class="col-sm-12">
                                            <textarea class="form-control" id="complain" name="complain" rows="3" spellcheck="false" required></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="col-md-12">
                                        <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Send</button>
                                    </div>
                                </div>

                            </div>
                        </form>


                    <?php } elseif (isset($form) && $form == "view") {
                        $general->markSeenUser($id);
                        $query = "SELECT * FROM `complains` where uid='" . userid . "' and id='$id'";
                        $result = mysqli_query($connection, $query);
                        $rows = mysqli_fetch_array($result);
                        if ($rows['status'] == 1)
                            $status = 'Open';
                        elseif ($rows['status'] == 2)
                            $status = 'Resolved';
                        ?>
                        <div class="table-responsive">
                        <?php if ($rows['status'] == 2) { ?>
                        <!-- <div class="text-right mb-2"><a href='?action=reopen&id=<?php echo $rows['id']; ?>' class='badge badge-info order'><i class='mdi mdi-pencil'></i> Re-Open Complain </a></div> -->
                        <?php } ?>
                            <div class="row">
                                <div class="col-sm-12">
                                    <table class="table">
                                        <tbody>
                                            <tr>
                                                <th>Course</th>
                                                <td>
                                                    <?php echo $general->getAttemptName($rows['attemptID']); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Title</th>
                                                <td>
                                                    <?php echo $rows['title']; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Status</th>
                                                <td>
                                                    <?php echo $status; ?>
                                                </td>
                                            </tr>
                                            <?php echo $general->getComplainReply($rows['id']);  ?>
                                            <?php if ($rows['status'] == 1) { ?>
                                            <tr>
                                                <th>Reply</th>
                                                <td>
                                                <form  action="" class="mt-2"  method="post" enctype="multipart/form-data" data-parsley-validate>
                                                    <input type="hidden" name="id" value="<?php echo $id; ?>">
                                                    <div class="form-group mb-2">
                                                        <textarea class="form-control" name="complain" id="complain"></textarea>
                                                    </div>
                                                    <div class="form-check form-check-flat form-check-primary">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" name="resolved" value="1" class="form-check-input">
                                                            Mark as Resolved
                                                        <i class="input-helper"></i></label>
                                                    </div>
                                                    <div class="text-right">
                                                        <button type="submit" class="btn btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="reply">Reply</button>
                                                    </div>
                                                </form>
                                                </td>
                                            </tr>
                                            <?php } ?>

                                        </tbody>
                                    </table>
                                </div>
                            </div>


                        </div>
                    <?php } else { ?>

                        <div class="table-responsive">
                            <table class="table table2">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>Course</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php

                                        $query = "SELECT * FROM `complains` where uid='" . userid . "' order by status asc";

                                        // echo $query;
                                        $ee=0;
                                        $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {
                                            if ($rows['status'] == 1)
                                                $status = 'Open';
                                            elseif ($rows['status'] == 2)
                                                $status = 'Resolved';

                                                $isUnread = "";
                                                if($general->isUnreadUser($rows['id']))
                                                $isUnread = "class='bg-secondary text-white'";
    

                                            $ee ++;


                                            $action = "<a href='?action=view&id=" . $rows['id'] . "' class='badge badge-primary'><i class='mdi mdi-eye'></i> View </a>";


                                            echo "<tr $isUnread>
                                                <td>" . $ee . "</td>
                                                <td>" . $rows['title'] . "</td>
                                                <td>" . $general->getAttemptName($rows['attemptID']) . "</td>
                                                <td>" . $status . "</td>
                                                <td>$action</td>
                                                
                                            </tr>";
                                        }
                                        ?>

                                </tbody>
                            </table>
                        </div>
                    <?php } ?>
                </div>
            </div>
        </div>

    </div>
</div>




<!-- /page content -->

<?php



include("includes/footer.php");
?>