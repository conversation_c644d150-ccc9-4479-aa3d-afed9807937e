<?php
exit;
include('includes/config.php');
include("includes/generalFunctions.class.php");
$general = new generalFunctions($connection);
if (isset($_POST['action'])) {


  if ($_POST['action'] == "login") {
    $email = mysqli_real_escape_string($connection, $_POST['email']);
    $password = mysqli_real_escape_string($connection, $_POST['password']);
    $strSQL = mysqli_query($connection, "select id,fname,lname,type,password,status from users where email='" . $email . "'");
    echo "select id,fname,lname,type,password,status from users where email='" . $email . "'";
    if (mysqli_num_rows($strSQL) > 0) {
      $results = mysqli_fetch_array($strSQL);
      if (password_verify($password, $results['password'])) {
        if ($results['status'] == 2) {
          $l_message = alert("danger", "Your account is <b>Inactive</b> please check your email for account activation or contact us.");
        } else {
          //Login Sucessfully!!
          if ($results['type'] == 1) //admin
          {
            $_SESSION['userid'] = $results['id'];
            header("location: " . adminurl . "/dashboard.php");
          }
          if ($results['type'] == 2) //user
          {
            $_SESSION['userid'] = $results['id'];
            $token = $general->getToken(40);
            $_SESSION['token'] = $token;
            $general->saveToken($results['id'], $token);
            header("location: " . siteurl . "/dashboard.php");
          }
          if ($results['type'] == 3) //teacher
          {
            $_SESSION['userid'] = $results['id'];
            header("location: " . teacherurl . "/dashboard.php");
          }
        }
      } else {
        $l_message = alert("danger", "Invalid password!!");
      }
    } else {
      $l_message = alert("danger", "Invalid email or password!!");
    }
  }
}
?>





<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Login | <?php echo sitename; ?></title>
  <!-- plugins:css -->
  <link rel="stylesheet" href="assets/vendors/mdi/css/materialdesignicons.min.css">
  <link rel="stylesheet" href="assets/vendors/base/vendor.bundle.base.css">
  <!-- endinject -->
  <!-- plugin css for this page -->
  <!-- End plugin css for this page -->
  <!-- inject:css -->
  <link rel="stylesheet" href="assets/css/style.css">
  <link rel="stylesheet" href="assets/css/style2.css?1">
  <!-- endinject -->
  <link rel="shortcut icon" href="assets/images/favicon.png" />
</head>

<body>
  <div class="container-scroller">
    <div class="container-fluid page-body-wrapper full-page-wrapper">
      <div class="content-wrapper content-wrapper1 d-flex align-items-center auth px-0">
        <div class="row w-100 mx-0">
          <div class="col-lg-4 mx-auto">
            <div class="auth-form-light text-left px-4">
              <div class="brand-logo text-center">
                <img src="<?php echo websiteLogo; ?>" alt="logo">
              </div>
              <?php echo isset($l_message) ? $l_message : ''; ?>
              <h4>Login</h4>
              <form class="pt-3" action="login.php" method="post">
                <div class="form-group">
                  <input type="text" name="email" class="form-control form-control-lg" id="exampleInputEmail1" placeholder="Email" required="required">
                </div>
                <div class="form-group">
                  <input type="password" name="password" class="form-control form-control-lg" id="exampleInputPassword1" placeholder="Password" required="required">
                </div>
                <div class="mt-3">
                  <button type="submit" class="btn btn-block btn-kns btn-lg font-weight-medium auth-form-btn" name="action" value="login">SIGN IN</button>
                </div>
                <div class="my-2 d-flex justify-content-between align-items-center">
                  <div class="form-check">
                    <label class="form-check-label text-muted">
                      <!-- <input type="checkbox" class="form-check-input">
                      Keep me signed in -->
                    </label>
                  </div>
                  <a href="forgot.php" class="auth-link text-black">Forgot password?</a>
                </div>
                <div class="text-center mt-4 font-weight-light">
                  <!-- Don't have an account? <a href="signup.php" class="text-primary">Create</a> -->
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      <!-- content-wrapper ends -->
    </div>
    <!-- page-body-wrapper ends -->
  </div>
  <!-- container-scroller -->
  <!-- plugins:js -->
  <script src="assets/vendors/base/vendor.bundle.base.js"></script>
  <!-- endinject -->
  <!-- inject:js -->
  <script src="assets/js/off-canvas.js"></script>
  <script src="assets/js/hoverable-collapse.js"></script>
  <script src="assets/js/template.js"></script>
  <!-- endinject -->
</body>

</html>