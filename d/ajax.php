<?php 
include_once('includes/config.php');
include_once('includes/session.php');
include("includes/generalFunctions.class.php");

header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

$general = new generalFunctions($connection);

if (isset($_GET['action'])) {
    if ($_GET['action'] == "updateStatus") {
        $logID  = mysqli_real_escape_string($connection, $_GET['logID']);
        
        $general->updateVideoStatsWatched($logID);
        exit;
    }
}

?>