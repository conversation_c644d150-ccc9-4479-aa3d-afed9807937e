<?php
include_once('includes/config.php');
include_once('includes/session.php');
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['id'])) {
    $id = mysqli_real_escape_string($connection, $_REQUEST['id']);
    $lectureTopic = $general->getLectureTopic($id);
} else {
    echo notify("Some error occured please try again.");
    exit;
}
if (!$general->isEnrolled(userid, $general->getCourseIDFeomLectureID($id))) {
    echo notify("Sorry you are not enrolled in this course.");
    exit;
}
?>
<!DOCTYPE html>

<head>
    <title>Live session</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.2/plyr.css" />
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .ssspan {
            position: fixed;
            z-index: 1;
            color: black;
            padding: 5px;
            font-size: 24px;
        }

        .plyr iframe {
            transition: 0.2s filter linear;
        }

        .plyr.plyr--paused iframe {
            filter: blur(1.5rem);
        }
    </style>
</head>

<body>


    <?php
    $query  = "SELECT * FROM lecture where id=$id";
    $result = mysqli_query($connection, $query);
    if (mysqli_num_rows($result) > 0) {
        $rows   = mysqli_fetch_array($result);

        $meetingID = $rows['meetingID'];
    } else {
        echo "Some error occured please try again.";
    }
    ?>




    <div class="row">
        <div class="col-lg-10 col-xl-10 col-md-12 col-sm-12">
            <div class="b121">
                <div id="container">
                    <span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email ?></span>
                    <!-- <img width="160px" src="<?php echo websiteLogo; ?>" alt="logo" style="position: relative;z-index: 1;margin-right: 100px;float: right;" /> -->
                </div>
                <div id="player" data-plyr-provider="youtube" data-plyr-embed-id="<?php echo $meetingID; ?>"></div>
            </div>
        </div>
        <div class="col-lg-2 col-xl-2 col-md-12 col-sm-12">

            <div>
                <p class="mb-4 font-weight-normal">CHAT</p>
            </div>

            <div id="inboxArea" style="width: auto;overflow: auto;max-height: 500px;">
                <?php
                $query = "SELECT * FROM chat where lectureID='$id' and userid='" . userid . "' order by id desc";
                $result = mysqli_query($connection, $query);
                while ($rows = mysqli_fetch_array($result)) {
                    ?>
                    <div class="item-content flex-grow">

                        <p class="font-weight-light small-text text-muted mb-3 border-top-0 border-right-0 border border-left-0">
                            <?php echo $rows['message']; ?>
                        </p>
                    </div>

                <?php
                }
                ?>
            </div>

            <div class="container" style="bottom:30px;">
                <input type="text" name="" id="chatInput" style="border:1px solid black;" class="form-control">
                <small style="color:grey">Press Enter to send<small>
            </div>


        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.0.0/jquery.min.js" integrity="sha256-JmvOoLtYsmqlsWxa7mDSLMwa6dZ9rrIdtrrVYRnDRH0=" crossorigin="anonymous"></script>
    <script src="https://cdn.plyr.io/3.6.2/plyr.polyfilled.js"></script>
    <script>
        document.oncontextmenu = document.body.oncontextmenu = function() {
            return false;
        }

        // Change "{}" to your options:
        // https://github.com/sampotts/plyr/#options
        const player = new Plyr('#player', {
            rel: 0,
            showinfo: 0,
            iv_load_policy: 3,
            modestbranding: 0,
            fullscreen: {
                enabled: false
            }
        });
        // Expose player so it can be used from the console
        window.player = player;



        function moveDiv() {
            var $span = $("#random");

            $span.fadeOut(1000, function() {

                var maxLeft = $(".b121").width() - $span.width();
                var maxTop = $(".b121").height() - $span.height();
                var leftPos = Math.floor(Math.random() * (maxLeft + 1))
                var topPos = Math.floor(Math.random() * (maxTop + 1))
                if (topPos > 280)
                    topPos = 250;
                $span.css({
                    left: leftPos,
                    top: topPos
                }).fadeIn(1000);
            });
        };

        moveDiv();
        setInterval(moveDiv, 5000);




        $(document).ready(function() {

            setInterval(function() {
                check_session();
                if (document.querySelectorAll("#container").length && document.querySelectorAll("#random").length) {
                    
                } else {
                    $("#container").remove();
                    var container = `<div id="container" ><span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email ?></span></div>`;
                    $(".b121").prepend(container);
                }

                

            }, 10000);


            $('#chatInput').keypress(function(e) {
                if (e.which == 13) {
                    var message = $(this).val();
                    $("#chatInput").val("");
                    var chatBox = `
                    <div class="item-content flex-grow">
                        
                        <p class="font-weight-light small-text text-muted mb-3 border-top-0 border-right-0 border border-left-0">
                            ` + message + ` 
                        </p>
                    </div>
                    `;
                    $("#inboxArea").prepend(chatBox);
                    $.getJSON("inbox_check.php?action=message&message=" + message + "&lectureID=<?php echo $id; ?>", function(data) {
                        console.log(data);
                    });
                    return false;
                }
            });

        });

        function check_session() {
            $.getJSON("check_token.php?action=token", function(data) {
                if (data.return == 2) {
                    location.href = "logout.php?action=multiple+login+not+allowed";
                }
            });
        }
    </script>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-66894152-2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-66894152-2');
    </script>
    <!-- <img width="160px" src="<?php echo websiteLogo; ?>" alt="logo" style="position: relative;z-index: 1;margin-right: 100px;float: right;" /> -->
</body>

</html>