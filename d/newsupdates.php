<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
function fetchUrl($url){

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // You may need to add the line below
    // curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,false);
   
    $feedData = curl_exec($ch);
    curl_close($ch); 
   
    return $feedData;
   
   }
?>
<style>
    p {
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        line-height: 2rem;
    }
     .dtrxt{
        font-size: 20px;
        color: #625f5f;
    }
</style>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>News and Updates</h2>

                    </div>
                </div>


            </div>
        </div>
    </div>

    <div class="row">

        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <img src="assets/images/yticon.png" alt="" width="100px"> <span class="dtrxt">Updates</span>
                        </div>
                        <div class="col-6">
                            <img src="assets/images/fbicon.png" alt="" width="100px"> <span class="dtrxt">Updates</span>
                        </div>
                            <div class="col-12">
                                    <div class="row text-center">
                                        <?php
                                        $feed = $general->curl_get_contents("https://www.youtube.com/feeds/videos.xml?channel_id=UCQf6Q26vXPWxdE9xIEVlq4Q");
                                        $xml = new SimpleXmlElement($feed);
                                        
                                        foreach($xml->entry as $val){
                                            $vidID = str_replace("yt:video:","",$val->id);
                                            ?>

                                            <div class="col-12 p-4">
                                                <div class="mb-2"><?php echo $val->title; ?></div>
                                                <a class="various fancybox fancybox.iframe" href="https://www.youtube.com/embed/<?php echo $vidID; ?>?autoplay=1" title="<?php echo $val->title; ?>">
                                                    <img style="width:100%;" src="https://img.youtube.com/vi/<?php echo $vidID; ?>/0.jpg" alt="<?php echo $val->title; ?>">
                                                </a>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                                <!-- <div class="col-6">
                                    <div class="row text-center">
                                        <?php
                                        
                                        $json_object1 = fetchUrl("https://graph.facebook.com/v11.0/214393042014994/feed?fields=picture,full_picture,message,created_time,status_type&limit=13&access_token=EAAUD94uzZBbwBAAzyJpKZB6bi6nmPCQNJSPXBnmhb9qgFDtJLTIc3F8u5wfNId1V2PUhWqZC2gbboYFcT0ZAY3FIOvyVuf4M4PgpHFWhfLeTESTB8370BKA6g6urICWd33ZB7YH0bJwgaCMQFiiacGXBTlmOKTJ2j4FIBHXSgxOgt0VOg8j7ymGNqmyp5WKoZD");

                                        $feedarray = json_decode($json_object1);
                                     
                                     foreach ( $feedarray->data as $feed_data )
                                     {
                                         if($feed_data->status_type == "shared_story")
                                            continue;
                                            ?>

                                            <div class="col-12 p-4">
                                                <div class="mb-2 text-left"><?php echo $feed_data->message; ?> <a target="_blank" href="https://www.facebook.com/<?php echo $feed_data->id; ?>" title="View on Facebook">View on Facebook</a></div>
                                                <?php if(isset($feed_data->full_picture)){ ?>
                                                <a class="various fancybox fancybox.iframe" href="<?php echo $feed_data->full_picture; ?>" title="<?php echo $feed_data->message; ?>">
                                                    <img style="width:100%;" src="<?php echo $feed_data->full_picture; ?>" alt="<?php echo $feed_data->message; ?>">
                                                </a>
                                                <?php } ?>
                                            </div>

                                            <?php ?>
                                        <?php } ?>
                                    </div>
                                    </div> -->
                                
                </div>
            </div>

        </div>
    </div>
</div>

<!-- /page content -->
<?php

$footerJS = "

<script type='text/javascript' src='assets/js/jquery.fancybox.js?v=2.1.5'></script>
<link rel='stylesheet' type='text/css' href='assets/css/jquery.fancybox.css?v=2.1.5' media='screen' />
<script>
  $('.fancybox').fancybox({
    openEffect	: 'elastic',
    closeEffect	: 'elastic',
    
    helpers : {
        title : {
            type : 'inside' 
        }    
    },
    beforeLoad: function() {
        this.title = this.title;
       }
});
</script>";
include("includes/footer.php");
?>