<?php
include_once('includes/config.php');
include_once('includes/session.php');
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['id'])) {
    $id = mysqli_real_escape_string($connection, $_REQUEST['id']);
    // $lectureTopic = $general->getLectureTopic($id);
} else {
    echo notify("Some error occured please try again.");
    exit;
}
// $attemptID = $general->getAttemptFromLectureID($id);


function get_client_ip() {
    $ipaddress = '';
    if (getenv('HTTP_CLIENT_IP'))
        $ipaddress = getenv('HTTP_CLIENT_IP');
    else if(getenv('HTTP_X_FORWARDED_FOR'))
        $ipaddress = getenv('HTTP_X_FORWARDED_FOR');
    else if(getenv('HTTP_X_FORWARDED'))
        $ipaddress = getenv('HTTP_X_FORWARDED');
    else if(getenv('HTTP_FORWARDED_FOR'))
        $ipaddress = getenv('HTTP_FORWARDED_FOR');
    else if(getenv('HTTP_FORWARDED'))
       $ipaddress = getenv('HTTP_FORWARDED');
    else if(getenv('REMOTE_ADDR'))
        $ipaddress = getenv('REMOTE_ADDR');
    else
        $ipaddress = 'UNKNOWN';
    return $ipaddress;
}

// error_reporting(E_ALL);
// ini_set('display_errors', '1');
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title><?php echo sitename; ?></title>
    <link rel="stylesheet" href="assets/vendors/mdi/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="assets/vendors/base/vendor.bundle.base.css">    
    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.2/plyr.css" />
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        .ssspan {
            position: fixed;
            z-index: 1;
            color: black;
            padding: 5px;
            font-size: 13px;
        }

        .plyr iframe {
            transition: 0.2s filter linear;
        }

        .plyr.plyr--paused iframe {
            /* filter: blur(1.5rem); */
        }
        .plyr__video-embed iframe {
            top: -50%;
            height: 200%;
        }
        .plyr__control--overlaid svg
        {
            left: 30px;
        }
        .plyr--full-ui.plyr--video .plyr__control--overlaid
        {
            width: 100px;
            height: 100px;
        }
    </style>
</head>


<body style="background-color: #b8d877;">
   



                <?php
                $query  = "SELECT * FROM lecture where id=$id";
                $result = mysqli_query($connection, $query);
                if (mysqli_num_rows($result) > 0) {
                    $rows   = mysqli_fetch_array($result);
                    // print_r($rows);
                    $meetingID = $rows['meetingID'];
                    $duration = $rows['duration'];
                    $chapterID = $rows['chapterID'];
                    $sortMe = $rows['sortMe'];
                    $id = $rows['id'];

                    if($duration == 0)
                        $duration = $general->updateVideoDuration($id,$meetingID);
                    

                    $videoLog  = "SELECT * FROM `videoStats` where vid=$id and uid=".userid;
                    $resultVL = mysqli_query($connection, $videoLog);
                    if (mysqli_num_rows($resultVL) > 0) {
                        $rows2   = mysqli_fetch_array($resultVL);
                        $logID = $rows2['id'];
                    }
                    else
                    {
                        $videoLog = "INSERT INTO `videoStats`(`uid`, `vid`, `duration`, `watched`) VALUES (".userid.", $id, $duration, 0)";
                        mysqli_query($connection, $videoLog);
                        $logID = mysqli_insert_id($connection);
                        
                    }

                    // Next Lecture
                    $nextLecture = "";
                    $sqlNext = "SELECT * FROM `lecture` WHERe chapterID=$chapterID and id=(select min(id) from `lecture` where chapterID=$chapterID and id > $id)";
                    // echo $sqlNext;

                    $resultNext = mysqli_query($connection, $sqlNext);
                    if (mysqli_num_rows($resultNext) > 0) {
                        $rowsNext   = mysqli_fetch_array($resultNext);
                        $nextLecture = "<a id='nextVideo' style='display:none;' href='recPlay.php?id=".$rowsNext['id']."'><img width='250px' src='next.gif?n=2' alt='Next Lecture' style='position: absolute;z-index: 1;bottom: 25px;right: 25px;' ></a>";
                    }


                } else {
                    echo "Some error occured please try again.";
                }
                
                
                
                
                ?>

<div>
        <!-- Just an image -->

        <?php 
$query = "SELECT * FROM `lecture` where `id`='" . $id . "'";
// echo $query;
$result22 = mysqli_query($connection, $query);
$row22 = mysqli_fetch_array($result22);

        ?>
<nav class="navbar navbar-light bg-light" style="background-color: #406032 !important;">
  <a class="navbar-brand" href="lecture.php?chapterID=<?php echo $rows['chapterID']; ?>" style="color:#b8d877;">
  <i class="mdi mdi-arrow-left"></i> Back &nbsp;&nbsp;&nbsp; <?php 
  if($row22['details'] != "")
    echo $row22['topic']." <small>(".$row22['details'].")<small>";
  else
    echo $row22['topic'];
  ?>
  </a>
</nav>
    </div>
<br />

                <div class="row">
                    <div class="col-lg-10 col-xl-10 col-md-10 col-sm-12" style="margin: auto;">
                        <div class="b121">
                            <div id="container">
                                
                                <?php echo $nextLecture; ?>
                                <!-- <img width="150px" src="<?php echo websiteLogo; ?>" alt="logo" style="opacity: 0.5;position: absolute;z-index: 1;top: -30px;left: -15px;" /> -->
                                <!-- <img width="150px" src="assets/images/blur.png" class="blurSticker" alt="blur" style="display:none;position: absolute;z-index: 1;bottom: 0;right: 10px;" />
                                <img width="170px" src="assets/images/blur.png" class="blurSticker" alt="blur" style="display:none;filter: blur(.4rem);position: absolute;z-index: 1;top: 0;right: 10px;" /> -->
                            </div>
                            <div id="player" data-plyr-provider="youtube" data-poster="poster1.jpeg" data-plyr-embed-id="<?php echo trim($meetingID); ?>"></div>
                        </div>
                    </div>
                </div>


                <script>if (typeof module === 'object') {window.module = module; module = undefined;}</script>
                <script src="jquery.js"></script>    
                
                <script>if (window.module) module = window.module;</script>

                </script>
                <script src="https://cdn.plyr.io/3.6.3/plyr.js"></script>
                <script>


                    document.onkeydown = function (t) {
                    if(t.which == 9){
                        return false;
                        }
                    }



                    document.oncontextmenu = document.body.oncontextmenu = function() {
                        return false;
                    }

                    // Change "{}" to your options:
                    // https://github.com/sampotts/plyr/#options
                    const player = new Plyr('#player', {
                        rel: 0,
                        showinfo: 0,
                        origin: 'https://www.knsinstitute.com',
                        iv_load_policy: 3,
                        modestbranding: 0,
                        fullscreen: {
                            enabled: true
                        }
                    });
                    // Expose player so it can be used from the console
                    window.player = player;

                    player.on('pause', event => {
                        const instance = event.detail.plyr;
                        // $('.blurSticker').show();
                    });
                    player.on('play', event => {
                        const instance = event.detail.plyr;
                        // $('.blurSticker').hide();
                    });
                    
                    function nextVideoRemove() {
                        $("#nextVideo").remove();
                    }
                    function moveDiv() {
                        var $span = $("#random");

                        $span.fadeOut(1000, function() {
                            $(".plyr__poster").css('background-image', 'url("poster.jpeg")')
                            
                            var maxLeft = $(".plyr").width() - $span.width();
                            var maxTop = $(".plyr").height() - $span.height();
                            var leftPos = Math.floor(Math.random() * (maxLeft + 1))
                            var topPos = Math.floor(Math.random() * (maxTop + 1))

                            $span.css({ left: leftPos, top: topPos }).fadeIn(1000);

                        });
                    };

                    moveDiv();
                    setInterval(moveDiv, 5000);
                    setInterval(updateDuration, 10000);
                    



                    $(document).ready(function() {
                        $('.plyr').append('<span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email."<br>".get_client_ip() ?></span>');
                        setInterval(function() {
                            check_session();
                            
                            if (document.querySelectorAll("#container").length && document.querySelectorAll("#random").length) {

                            } else {
                                $("#container").remove();
                                var container = `<div id="container" ><span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email."<br>".get_client_ip() ?></span>`;
                                $(".b121").prepend(container);
                            }



                        }, 10000);


                    });
                    player.on("ended", function(){
                        updateStatusOfLog();
                    })
                    player.on("seeked", function(){
                        if((player.duration-120) < player.currentTime)
                            updateStatusOfLog();
                        else
                            $("#nextVideo").css('display','none');
                    })
                    function updateDuration() {
                        // console.log("dd Duration: ",player.duration);
                        // console.log("dd Current: ",player.currentTime);

                        // console.log("dd Duration: ",player.duration-120);
                        // console.log("dd Current: ",player.currentTime);
                        if((player.duration-120) < player.currentTime)
                            updateStatusOfLog();
                    }
                    function check_session() {
                        // 
                        $.getJSON("check_token.php?action=token", function(data) {
                            if (data.return == 2) {
                                location.href = "logout.php?action=multiple+login+not+allowed";
                            }
                        });
                    }
                    function updateStatusOfLog() {
                        $("#nextVideo").css('display','');
                        setInterval(nextVideoRemove, 30000);
                        $.getJSON("ajax.php?action=updateStatus&logID=<?php echo $logID; ?>", function(data) {});
                    }
                </script>
              <br />  
        <footer class="footer">
          <div class="d-sm-flex justify-content-center justify-content-sm-between">
            <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">&copy;<?php echo date("Y"); ?> All Rights Reserved. <?php echo sitename; ?>.</span>
            <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center">Designed & Developed by <a href="https://www.phplift.net/" target="_blank">PHPLift.net</a></span>
          </div>
        </footer>
        <!-- partial -->
      </div>
      <!-- main-panel ends -->
    </div>
    <!-- page-body-wrapper ends -->
  </div>
   <!-- Global site tag (gtag.js) - Google Analytics -->
   <script async src="https://www.googletagmanager.com/gtag/js?id=UA-66894152-2"></script>
                <script>
                    window.dataLayer = window.dataLayer || [];

                    function gtag() {
                        dataLayer.push(arguments);
                    }
                    gtag('js', new Date());

                    gtag('config', 'UA-66894152-2');
                </script>           
</body>

</html>