<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);


?>
<style>
  @-webkit-keyframes blinker {
    0% {
      opacity: 1.0;
    }

    50% {
      opacity: 0.0;
    }

    100% {
      opacity: 1.0;
    }
  }

  .blink {
    width: 10px;
    height: 10px;
    border-radius: 10px;
    animation: blinker 2s linear infinite;
    background-color: red;
    margin-right: 5px;
  }

  .content {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
</style>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Live Lectures</h2>

          </div>
        </div>

      </div>
    </div>
  </div>

  <div class="row">
    <?php
    $query = "SELECT lecture.id as lecture_id,lecture.start_time as lecture_start_time ,topic from lecture,enrolled where enrolled.attempt=lecture.chapterID and enrolled.userid='" . userid . "' and lecture.type=1";
    // echo  $query;
    $result = mysqli_query($connection, $query);
    if (mysqli_num_rows($result) > 0) {
      while ($rows = mysqli_fetch_array($result)) {

        ?>


        <div class="col-md-4 grid-margin">
          <div class="card d-flex align-items-center">
            <div class="card-body">
              <div class="content" style="float: right;color:#b8d877;">
                <i class="blink"></i>
                LIVE
              </div>
              <a href='livePlay_zoom.php?id=<?php echo $rows['lecture_id']; ?>' class="nav-link">
                <div class="d-flex flex-row align-items-center" style="height: 120px;">
                  <i class="mdi mdi mdi-access-point text-youtube icon-md"></i>
                  <div class="ml-3">
                    <h6 class="text-youtube"><?php echo $rows['topic']; ?></h6>
                    <p class="mt-2 text-muted card-text">Time: <?php echo date("d/m/Y H:i:s A", strtotime($rows['lecture_start_time'])); ?></p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>



      <?php }
      } else {
        ?>


      <div class="col-lg-12 grid-margin stretch-card">
        <div class="card card2">
          <div class="card-body">
            <div class="text-center d-block w-100">
              <p><strong>Coming soon!</strong></p>
            </div>
          </div>
        </div>
      </div>
    <?php
    }  ?>


  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>