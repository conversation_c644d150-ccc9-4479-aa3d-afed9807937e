<?php
include_once('includes/config.php');
include_once('includes/session.php');   
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['id'])) {
    $id = mysqli_real_escape_string($connection, $_REQUEST['id']);
    $lectureTopic = $general->getLectureTopic($id);
} 
else
{
    echo notify("Some error occured please try again.");
    exit;
}
if(!$general->isEnrolled(userid,$general->getCourseIDFeomLectureID($id))){
    echo notify("Sorry you are not enrolled in this course.");
    exit;
}
?>

<!DOCTYPE html>

<head>
    <title>Live session</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.7.9/css/bootstrap.css" />
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.7.9/css/react-select.css" />

    <style>
        .ssspan {
            position: fixed;
            z-index: 1;
            color: black;
            padding: 5px;
            font-size: 24px;
        }
        #wc-footer > div:nth-child(2) > button:nth-child(1)
        {
          display: none;
        }
        #wc-footer-left > div:nth-child(2) > button
        {
          display: none;
        }

       
    </style>
</head>








<body style="background-color: #b8d877;">
   

    <div id="container">
                                <span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email ?></span>
                                <img width="200px" src="<?php echo websiteLogo; ?>" alt="logo" style="position: absolute;z-index: 1;top: -30px;left: -15px;" />
                            </div>

  
        <?php
        $query  = "SELECT * FROM lecture where id=$id";
        $result = mysqli_query($connection, $query);
        if (mysqli_num_rows($result) > 0) {
            $rows   = mysqli_fetch_array($result);

            ?>

            <?php $meetingID = $rows['meetingID']; ?>
            
        <?php }  ?>




  <script src="assets/vendors/base/vendor.bundle.base.js"></script>
  <!-- endinject -->
  <!-- inject:js -->
  <script src="assets/js/off-canvas.js"></script>
  <script src="assets/js/hoverable-collapse.js"></script>
  <script src="assets/js/template.js"></script>
  <script src="assets/js/script.js"></script>

  <script src="https://source.zoom.us/1.7.9/lib/vendor/react.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/react-dom.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/redux.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/redux-thunk.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/jquery.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/lodash.min.js"></script>

    <script src="https://source.zoom.us/zoom-meeting-1.7.9.min.js"></script>

    <script src="js/tool.js?i=7"></script>
    <!-- <script src="js/index.js"></script> -->

    <script>

document.oncontextmenu = document.body.oncontextmenu = function() {
                        return false;
                    }

function moveDiv() {
    var $span = $("#random");
    
    $span.fadeOut(1000, function() {
        var maxLeft = $(window).width() - $span.width();
        var maxTop = $(window).height() - $span.height();
        var leftPos = Math.floor(Math.random() * (maxLeft + 1))
        var topPos = Math.floor(Math.random() * (maxTop + 1))
     
        $span.css({ left: leftPos, top: topPos }).fadeIn(1000);
    });
};

moveDiv();
setInterval(moveDiv, 5000);



$(document).ready(function() {

setInterval(function() {
    check_session();
    
    if (document.querySelectorAll("#container").length && document.querySelectorAll("#random").length) {

    } else {
        $("#container").remove();
        var container = `<div id="container" ><span id="random" class="ssspan"><?php echo fname . " " . lname . ", " . email ?></span><img width="160px" src="<?php echo websiteLogo; ?>" alt="logo" style="position: fixed;z-index: 1;top: -30px;left: -15px;" /></div>`;
        $(".b121").prepend(container);
    }



}, 10000);


});

function check_session() {
$.getJSON("check_token.php?action=token", function(data) {
    if (data.return == 2) {
        location.href = "logout.php?action=multiple+login+not+allowed";
    }
});
}








  var API_KEY = "<?php echo apiKey;?>";

  

  




var signature = '<?php echo $general->generate_signature($meetingID, 0); ?>';


(function () {
  var testTool = window.testTool;
  // get meeting args from url
  var meetingConfig = {
    apiKey: "<?php echo apiKey;?>",
    meetingNumber: "<?php echo $meetingID; ?>",
    userName: "<?php echo fname;?>-web",
    passWord: "2euuUd",
    leaveUrl: "https://www.knsinstitute.com/dashboard.php",
    role: 0,
    userEmail: "<?php echo email;?>",
    lang: "en-US",
    signature: "<?php echo $general->generate_signature($meetingID, 0); ?>",
  };

  // a tool use debug mobile device
  if (testTool.isMobileDevice()) {
    vConsole = new VConsole();
  }
  console.log(JSON.stringify(ZoomMtg.checkSystemRequirements()));

  // it's option if you want to change the WebSDK dependency link resources. setZoomJSLib must be run at first
  // ZoomMtg.setZoomJSLib("https://source.zoom.us/1.7.9/lib", "/av"); // CDN version defaul
  if (meetingConfig.china)
    ZoomMtg.setZoomJSLib("https://jssdk.zoomus.cn/1.7.9/lib", "/av"); // china cdn option
  ZoomMtg.preLoadWasm();
  ZoomMtg.prepareJssdk();
  function beginJoin(signature) {
    ZoomMtg.init({
      leaveUrl: meetingConfig.leaveUrl,
      webEndpoint: meetingConfig.webEndpoint,
      success: function () {
        console.log(meetingConfig);
        console.log("signature", signature);
        $.i18n.reload(meetingConfig.lang);
        ZoomMtg.join({
          meetingNumber: meetingConfig.meetingNumber,
          userName: meetingConfig.userName,
          signature: signature,
          apiKey: meetingConfig.apiKey,
          userEmail: meetingConfig.userEmail,
          passWord: meetingConfig.passWord,
          success: function (res) {
            console.log("join meeting success");
            console.log("get attendeelist");
            ZoomMtg.getAttendeeslist({});
            ZoomMtg.getCurrentUser({
              success: function (res) {
                console.log("success getCurrentUser", res.result.currentUser);
              },
            });
          },
          error: function (res) {
            console.log(res);
          },
        });
      },
      error: function (res) {
        console.log(res);
      },
    });
  }

  beginJoin(meetingConfig.signature);
})();














    </script>
<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-66894152-2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-66894152-2');
</script>

    
</body>

</html>