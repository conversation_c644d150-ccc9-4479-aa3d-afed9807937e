<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['courseID']))
{
    $courseID = mysqli_real_escape_string($connection,$_REQUEST['courseID']);
    $className = $general->getCourseName($courseID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

?>

<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
          <h2>Attempts in <?php echo $className; ?></h2>
                        
          </div>
        </div>
        <div class="align-right"><h3><a href="course.php?levelID=<?php echo $general->getLevelIDbycourseID($courseID); ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>

      </div>
    </div>
  </div>

  <div class="row">
  <?php
    $query = "SELECT * FROM attempt where courseID=$courseID and status=1 order by sortMe";
    $result = mysqli_query($connection, $query);
    $id = 1;
    while ($rows = mysqli_fetch_array($result)) {
    
  ?>

<div class="col-md-6 col-lg-4 column">
        <div class="cardNew gr-2">
        <a href='chapters.php?attemptID=<?php echo $rows['id']; ?>' class="nav-link">
          <div class="txt">
            <h1> <?php echo $general->getAttemptName($rows['id']); ?><?php //echo $rows['title']; ?> </h1>
            <p><?php echo nl2br($rows['desc']); ?></p>
          </div>
        </a>
          <div class="ico-card">
            <i class="fa fa-video-camera"></i>
          </div>
        </div>
      </div>



    <?php }  ?>
  
   
</div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>