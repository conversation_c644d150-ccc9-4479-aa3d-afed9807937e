





<?php 
  

exit;
?> 
<!DOCTYPE html>
<head>
    <title>Zoom session</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.7.9/css/bootstrap.css" />
    <link type="text/css" rel="stylesheet" href="https://source.zoom.us/1.7.9/css/react-select.css" />
</head>

<body style="background-color: #b8d877;">

  <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script src="assets/vendors/base/vendor.bundle.base.js"></script>
  <!-- endinject -->
  <!-- inject:js -->
  <script src="assets/js/off-canvas.js"></script>
  <script src="assets/js/hoverable-collapse.js"></script>
  <script src="assets/js/template.js"></script>
  <script src="assets/js/script.js"></script>

  <script src="https://source.zoom.us/1.7.9/lib/vendor/react.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/react-dom.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/redux.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/redux-thunk.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/jquery.min.js"></script>
    <script src="https://source.zoom.us/1.7.9/lib/vendor/lodash.min.js"></script>

    <script src="https://source.zoom.us/zoom-meeting-1.7.9.min.js"></script>

    <script src="js/tool.js?i=7"></script>
    <!-- <script src="js/index.js"></script> -->

    <script>

document.oncontextmenu = document.body.oncontextmenu = function() {
                        return false;
                    }

var API_KEY = "TLdmS-7JTQWcZZhaq6jg8A";
var signature = 'VExkbVMtN0pUUVdjWlpoYXE2amc4QS44MTM5NTY0MzIzMy4xNTk1NDg2MDkyMDAwLjAuN2hyVHJmNEYveUUvand3THhHMmZBUEdWVDhGU1o5RDhXMGc1ZnpZTzNWTT0';


(function () {
  var testTool = window.testTool;
  // get meeting args from url
  var meetingConfig = {
    apiKey: "TLdmS-7JTQWcZZhaq6jg8A",
    meetingNumber: "81395643233",
    userName: "HB-web",
    passWord: "2euuUd",
    leaveUrl: "https://www.knsinstitute.com/",
    role: 0,
    userEmail: "<EMAIL>",
    lang: "en-US",
    signature: "VExkbVMtN0pUUVdjWlpoYXE2amc4QS44MTM5NTY0MzIzMy4xNTk1NDg2MDkyMDAwLjAuN2hyVHJmNEYveUUvand3THhHMmZBUEdWVDhGU1o5RDhXMGc1ZnpZTzNWTT0",
  };

  // a tool use debug mobile device
  if (testTool.isMobileDevice()) {
    vConsole = new VConsole();
  }
  console.log(JSON.stringify(ZoomMtg.checkSystemRequirements()));

  // it's option if you want to change the WebSDK dependency link resources. setZoomJSLib must be run at first
  // ZoomMtg.setZoomJSLib("https://source.zoom.us/1.7.9/lib", "/av"); // CDN version defaul
  if (meetingConfig.china)
    ZoomMtg.setZoomJSLib("https://jssdk.zoomus.cn/1.7.9/lib", "/av"); // china cdn option
  ZoomMtg.preLoadWasm();
  ZoomMtg.prepareJssdk();
  function beginJoin(signature) {
    ZoomMtg.init({
      leaveUrl: meetingConfig.leaveUrl,
      webEndpoint: meetingConfig.webEndpoint,
      success: function () {
        console.log(meetingConfig);
        console.log("signature", signature);
        $.i18n.reload(meetingConfig.lang);
        ZoomMtg.join({
          meetingNumber: meetingConfig.meetingNumber,
          userName: meetingConfig.userName,
          signature: signature,
          apiKey: meetingConfig.apiKey,
          userEmail: meetingConfig.userEmail,
          passWord: meetingConfig.passWord,
          success: function (res) {
            console.log("join meeting success");
            console.log("get attendeelist");
            ZoomMtg.getAttendeeslist({});
            ZoomMtg.getCurrentUser({
              success: function (res) {
                console.log("success getCurrentUser", res.result.currentUser);
              },
            });
          },
          error: function (res) {
            console.log(res);
          },
        });
      },
      error: function (res) {
        console.log(res);
      },
    });
  }

  beginJoin(meetingConfig.signature);
})();
</script>
</body>
</html>