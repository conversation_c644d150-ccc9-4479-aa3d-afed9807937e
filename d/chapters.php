<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['attemptID']))
{
    $attemptID = mysqli_real_escape_string($connection,$_REQUEST['attemptID']);
    $className = $general->getAttemptName($attemptID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

?>
<style>
  
  .styled {
    margin-top: 2px;
    margin-left: 10px;
    font-size: 15px;
  }
  .bigFont {
    font-size: 17px;
  }
</style>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          
        </div>
        
          
            <div class="align-right"><h3><a href="attempt.php?courseID=<?php echo $general->getcourseIDbyattemptID($attemptID); ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>
            

      </div>
    </div>
  </div>

  
    <?php 
  if(!$general->isEnrolled(userid,$attemptID)){
    echo '<div class="row">';
    echo notify("Sorry you are not enrolled in this course.");
    echo '</div>';
    exit;
}
else{


    
  ?>


   
<div class="row">
    
    <div class="col-md-12 col-lg-12 column mb-2">
      
      <ul class="list-group">
        <li class="list-group-item">
          <div class="mr-md-3 mr-xl-5">
            <h2><?php echo $className; ?></h2>
          </div>
        </li>
        <?php

          $query = "SELECT * FROM chapters where attemptID=$attemptID and status=1 order by sortMe";
          $result = mysqli_query($connection, $query);
          $id = 1;
          while ($rows = mysqli_fetch_array($result)) {
        ?>
           
            <li class="list-group-item">
            <div class="d-flex">
              <i class="mdi mdi-book-open-page-variant menu-icon"></i>
              <a href='lecture.php?chapterID=<?php echo $rows['id']; ?>'>
                <p class="styled bigFont"> <?php echo $rows['title']; ?> (<?php echo $general->getLecturesCountbyChapter($rows['id']) ?> Lecture(s) ) </p>
              </a>
            </div>
            
              <p> <?php echo nl2br($rows['desc']); ?> </p>
              <p class="mt-2 ml-2 text-muted"><a style="margin-left: 15px;font-size: 1rem;">Total Handouts (<?php echo $general->getNumberOfHandoutInChapter($rows['id']) ?>)</a></p>
            </li>  
          <?php

          } }
          ?>
      </ul>

    </div>
  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>