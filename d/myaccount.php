<?php
$extraCSS = "
<style>
.container{
padding: 15px;
}
.tab-pane{
    border: solid 1px beige;
}
</style>";
include("includes/header.php");
include("includes/generalFunctions.class.php");
$general = new generalFunctions($connection);


if (isset($_GET['action'])) {
  if ($_GET['action'] == "edit") {
    $form = "edit";
  }
}
if (isset($_POST['action'])) {
  if ($_POST['action'] == "password") {
    $password = mysqli_real_escape_string($connection, $_POST['password']);
    $npassword = mysqli_real_escape_string($connection, $_POST['npassword']);
    $rpassword = mysqli_real_escape_string($connection, $_POST['rpassword']);

    $strSQL = mysqli_query($connection, "select id,password from users where id='" . userid . "'");
    if (mysqli_num_rows($strSQL) > 0) {
      $results = mysqli_fetch_array($strSQL);
      if (password_verify($password, $results['password'])) {
        if ($npassword != $rpassword) {
          $message = '<div class="alert alert-danger" role="alert">New password dosen\'t match</div>';
        } else {
          $password     = password_hash(mysqli_real_escape_string($connection, $npassword), PASSWORD_DEFAULT);
          $query = "update users set password='" . $password . "' where id='" . userid . "'";

          mysqli_query($connection, $query);
          $message = '<div class="alert alert-success" role="alert">Your password changed sucessfully </div>';
        }
      } else {
        $message = '<div class="alert alert-danger" role="alert">Invalid current password</div>';
      }
    } else {
      $message = '<div class="alert alert-danger" role="alert">Internal error please try again</div>';
    }
  }
  if ($_POST['action'] == "edit") {
    $fname  = mysqli_real_escape_string($connection, $_POST['fname']);
    $lname  = mysqli_real_escape_string($connection, $_POST['lname']);
    $email  = mysqli_real_escape_string($connection, $_POST['email']);
    $phone = mysqli_real_escape_string($connection, $_POST['phone']);

    $query = "SELECT email FROM users where id='" . userid . "'";
    $result = mysqli_query($connection, $query);
    $numResults = mysqli_num_rows($result);

    if ($numResults > 0) {
      $results = mysqli_fetch_array($result);
      if ($results['email'] != $email) {
        $query = "SELECT email FROM users where email='" . $email . "'";
        $result = mysqli_query($connection, $query); {
          $numResults = mysqli_num_rows($result);
          if ($numResults > 0) {
            $email = $results['email'];
            $message = '<div class="alert alert-danger" role="alert">Email address already exist</div>';
          }
        }
      } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
      {
        $message = '<div class="alert alert-danger" role="alert">Invalid email address please type a valid email</div>';
      } else {
        $query = "update users set fname='$fname', lname='$lname', email='$email', phone='$phone' where id='" . userid . "'";
        $result = mysqli_query($connection, $query);
        $message = '<div class="alert alert-success" role="alert">Account updated</div>';
      }
    } else {
      $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
    }
  }
}

$strSQL = mysqli_query($connection, "select id,fname,lname,email,phone from users where id='" . userid . "'");
if (mysqli_num_rows($strSQL) > 0) {
  $results = mysqli_fetch_array($strSQL);
  $fname  = $results['fname'];
  $lname  = $results['lname'];
  $email  = $results['email'];
  $phone = $results['phone'];
}
?>
<!-- page content -->
<div class="content-wrapper">
  <div class="row">
    <div class="col-md-12 col-xl-12 grid-margin stretch-card">
      <div class="card card2">

        <div class="card-body">
          <?php echo isset($message) ? $message : ''; ?>
          <h4 class="card-title">My Account</h4>
          <p class="card-description">Update your account information</p>
          <ul class="nav nav-tabs" role="tablist">
            <li class="nav-item">
              <a class="nav-link <?php echo (!isset($_POST['action']) || (isset($_POST['action']) && $_POST['action'] == "edit")) ? 'active' : ''; ?>" id="home-tab" data-toggle="tab" href="#home-1" role="tab" aria-controls="home-1" aria-selected="<?php echo (!isset($_POST['action']) || (isset($_POST['action']) && $_POST['action'] == "edit")) ? 'true' : 'false'; ?>">Account Info</a>
            </li>
            <li class="nav-item">
              <a class="nav-link <?php echo (isset($_POST['action']) && $_POST['action'] == "password") ? 'show active' : ''; ?>" id="profile-tab" data-toggle="tab" href="#profile-1" role="tab" aria-controls="profile-1" aria-selected="<?php echo (isset($_POST['action']) && $_POST['action'] == "password") ? 'true' : 'false'; ?>">Change Password</a>
            </li>
          </ul>
          <div class="tab-content">
            <!-- profile -->
            <div class="tab-pane fade <?php echo (!isset($_POST['action']) || (isset($_POST['action']) && $_POST['action'] == "edit")) ? 'show active' : ''; ?>" id="home-1" role="tabpanel" aria-labelledby="home-tab">
              <div class="container">
                <div id="profile_edit" style="display:none;">
                  <form id="form" action="myaccount.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                    <div class="form-group">
                      First Name: <input type="text" name="fname" value="<?php echo isset($fname) ? $fname : ''; ?>" class="form-control form-control-lg" placeholder="First Name" required="required">
                    </div>
                    <div class="form-group">
                      Last Name: <input type="text" name="lname" value="<?php echo isset($lname) ? $lname : ''; ?>" class="form-control form-control-lg" placeholder="Last Name" required="required">
                    </div>
                    <div class="form-group">
                      Email: <input type="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" class="form-control form-control-lg" placeholder="Email" required="required">
                    </div>
                    <div class="form-group">
                      Phone: <input type="tel" name="phone" value="<?php echo isset($phone) ? $phone : ''; ?>" class="form-control form-control-lg" placeholder="Phone" required="required">
                    </div>
                    <div class="mt-3">
                      <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="edit">Save</button>
                    </div>
                  </form>
                </div>

                <div id="profile">
                  <div style="position:absolute;">
                    <a href="javascript:;" class="badge badge-info profile" data-id="profile_edit"><i class="mdi mdi-pencil"></i> Edit</a>
                  </div>

                  
                  <table class="table table2 text-center">
                    <tr>
                      <th>First Name</th>
                      <td><?php echo $fname . " " . $lname; ?></td>
                    </tr>
                    <tr>
                      <th>Email</th>
                      <td><?php echo $email; ?></td>
                    </tr>
                    <tr>
                      <th>Phone</th>
                      <td><?php echo $phone; ?></td>
                    </tr>
                  </table>
                </div>


              </div>
            </div>
            <!-- password -->
            <div class="tab-pane fade <?php echo (isset($_POST['action']) && $_POST['action'] == "password") ? 'show active' : ''; ?>" id="profile-1" role="tabpanel" aria-labelledby="profile-tab">
              <div class="container">
                <form id="form" action="myaccount.php" method="post" data-parsley-validate class="form-horizontal form-label-left">
                  <div class="form-group">
                    Current Password: <input type="password" name="password" class="form-control form-control-lg" placeholder="Current Password" required="required">
                  </div>
                  <div class="form-group">
                    New Password: <input type="password" name="npassword" class="form-control form-control-lg" placeholder="New Password" required="required">
                  </div>
                  <div class="form-group">
                    Re-type new Password: <input type="password" name="rpassword" class="form-control form-control-lg" placeholder="Re-type new password" required="required">
                  </div>
                  <div class="mt-3">
                    <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="password">Save</button>
                  </div>
                </form>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</div>




<!-- /page content -->

<?php

$footerJS = "<script>
  $('.profile').click(function(){
    if($(this).data('id') == 'profile_edit'){
      $('#profile').hide();
      $('#profile_edit').show();
    }
    if($(this).data('id') == 'profile'){
      $('#profile').show();
      $('#profile_edit').hide();
    }
  });
</script>";

include("includes/footer.php");
?>