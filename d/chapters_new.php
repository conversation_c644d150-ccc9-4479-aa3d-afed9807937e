<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if(isset($_REQUEST['attemptID']))
{
    $attemptID = mysqli_real_escape_string($connection,$_REQUEST['attemptID']);
    $className = $general->getAttemptName($attemptID);
}
else
{
    echo "some error occured please try again.";
    exit;
}

?>
<style>
  
  .styled {
    margin-top: 2px;
    margin-left: 10px;
    font-size: 15px;
  }
  .bigFont {
    font-size: 17px;
  }
</style>
<!-- page content -->
<div class="content-wrapper">

  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          
        </div>
        
          
        <div class="align-right"><h3><a href="dashboard.php"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>
            

      </div>
    </div>
  </div>

  
    <?php 
  if(!$general->isEnrolled(userid,$attemptID)){
    echo '<div class="row">';
    echo notify("Sorry you are not enrolled in this course.");
    echo '</div>';
    exit;
}
else{

  $query = "SELECT * FROM `attempt` where `id`=$attemptID";
  $resultAttempt = mysqli_query($connection, $query);
  $rowsAttempt = mysqli_fetch_array($resultAttempt);
  if($rowsAttempt['faculty'] > 0)
  {
    $query = "SELECT * FROM `faculty` where `id`='" . $rowsAttempt['faculty'] . "'";
    $resultFaculty = mysqli_query($connection, $query);
    $resultFaculty = mysqli_fetch_array($resultFaculty);

  }
    
  ?>
<?php if($rowsAttempt['faculty'] > 0){ ?>
<!-- Modal -->
<div class="modal fade" id="faculty" tabindex="-1" role="dialog" aria-labelledby="facultyLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Faculty Profile</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <h1><?php echo $resultFaculty['name']; ?></h1>
        <?php echo $resultFaculty['profile']; ?>

      </div>
      
    </div>
  </div>
</div>
<?php } ?>
<?php if($rowsAttempt['planner'] != ""){ ?>
<!-- Modal -->
<div class="modal fade" id="planner" tabindex="-1" role="dialog" aria-labelledby="plannerLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Teaching Plan</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
      
      
      <!-- <embed src="" width="100%" height="600px" /> -->
      <iframe src="<?php echo "https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/planner/".$rowsAttempt['planner']; ?>" height="600px" width="100%" scrolling="auto"></iframe>

      </div>
      
    </div>
  </div>
</div>
<?php } ?>
<?php if($rowsAttempt['syllabus'] != ""){ ?>
<!-- Modal -->
<div class="modal fade" id="syllabus" tabindex="-1" role="dialog" aria-labelledby="syllabusLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Syllabus Outline</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
      <iframe src="<?php echo "https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/syllabus/".$rowsAttempt['syllabus']; ?>" height="600px" width="100%" scrolling="auto"></iframe>
      

      </div>
      
    </div>
  </div>
</div>
<?php } ?>

<div class="row">
    
    <div class="col-md-12 col-lg-12 column mb-2">
      
      <ul class="list-group">
        <li class="list-group-item">
          <div class="mr-md-3 mr-xl-5">
            <h2><?php echo $className; ?></h2>
          </div>
          <?php if($rowsAttempt['faculty'] > 0){ ?>
            <button type="button" class="btn btn-success btn-sm cbtn" data-toggle="modal" data-target="#faculty">Faculty Profile</button>
          <?php } ?>
          <?php if($rowsAttempt['syllabus'] != ""){ ?>
            <button type="button" class="btn btn-secondary btn-sm cbtn" data-toggle="modal" data-target="#syllabus">Syllabus Outline</button>
          <?php } ?>
          <?php if($rowsAttempt['planner'] != ""){ ?>
            <button type="button" class="btn btn-warning btn-sm cbtn" data-toggle="modal" data-target="#planner">Teaching Plan</button>
          <?php } ?>
        </li>
        
          <?php
          $query = "SELECT * FROM booklet where attemptID=$attemptID order by sortMe";
          $resultBooklet = mysqli_query($connection, $query);
          if(mysqli_num_rows($resultBooklet) > 0)
          {
            echo "<li class=\"list-group-item\">
            <h4>Booklets</h4>";
            echo "<ul>";
            $liid= 1;
            while ($rowsBooklet = mysqli_fetch_array($resultBooklet))
            {
              
              echo "<li class='listBook'>";
              echo '<button type="button" class="btn btn-outline-dark btn-sm cbtn_n" data-toggle="modal" data-target="#booklet_'.$rowsBooklet['id'].'">'.$rowsBooklet['title'].'</button>';
              echo "</li>"; 
              ?>
                <div class="modal fade" id="booklet_<?php echo $rowsBooklet['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="syllabusLabel" aria-hidden="true">
                  <div class="modal-dialog" role="document">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo $rowsBooklet['title']; ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                          <span aria-hidden="true">&times;</span>
                        </button>
                      </div>
                      <div class="modal-body">
                        <iframe src="<?php echo "https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/booklet/".$rowsBooklet['booklet']; ?>" height="600px" width="100%" scrolling="auto"></iframe>
                      </div>
                    </div>
                  </div>
                </div>
              <?php
              
            }
            echo "</ul></li>";
          }
            // 
          ?>
        
        

        <?php

          $query = "SELECT * FROM chapters where attemptID=$attemptID and status=1 order by sortMe";
          $result = mysqli_query($connection, $query);
          $id = 1;
          
          while ($rows = mysqli_fetch_array($result)) {
            $duration = false;
            $sqlDuration = "select sum(duration) as duration from lecture WHERE chapterid=".$rows['id'];
            $resultDuration = mysqli_query($connection, $sqlDuration);
            if(mysqli_num_rows($resultDuration) > 0)
            {
              $rowsDuration = mysqli_fetch_array($resultDuration);
              if($rowsDuration['duration']>0)
              $duration = "<i class=\"mdi mdi-clock menu-icon clockSize\"></i> Duration: ". gmdate("H:i:s", $rowsDuration['duration']);
            }
        ?>
           
            <li class="list-group-item">
            <div class="d-flex justify-content-between">
            <div>
            <div class="d-flex">
              <i class="mdi mdi-book-open-page-variant menu-icon"></i>
              <a href='lecture.php?chapterID=<?php echo $rows['id']; ?>'>
                <p class="styled bigFont"> <?php echo $rows['title']; ?>  </p>
              </a>
            </div>
            
              <p> <?php echo nl2br($rows['desc']); ?> (<?php echo $general->getLecturesCountbyChapter($rows['id']) ?> Lecture(s) ) &nbsp;&nbsp;&nbsp;&nbsp;  <?php echo $duration; ?></p>
              <p class="mt-2 ml-2 text-muted"><a style="margin-left: 15px;font-size: 1rem;">Total Handouts (<?php echo $general->getNumberOfHandoutInChapter($rows['id']) ?>)</a></p>
              
              </div>
              <?php
              $sqlQuiz = "select * from quiz WHERE chapterID=".$rows['id'];
              $resultQuiz = mysqli_query($connection, $sqlQuiz);
              if(mysqli_num_rows($resultQuiz) > 0)
              {
                  echo "<div class='card' style='width: 275px;background-color: #fff;'><ul><h6><li class='listBook'>Quiz/Test</li></h6>";
                  while($rowsQuiz = mysqli_fetch_array($resultQuiz))
                  {
                      echo "<li class='listBook'>";
                      echo '<button type="button" class="btn btn-outline-dark btn-sm cbtn_n" data-toggle="modal" data-target="#booklet_'.$rowsQuiz['id'].'">'.$rowsQuiz['title'].'</button>';
                      echo "</li>"; 
                      ?>
                      <div class="modal fade" id="booklet_<?php echo $rowsQuiz['id']; ?>" tabindex="-1" role="dialog" aria-labelledby="syllabusLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                          <div class="modal-content">
                            <div class="modal-header">
                              <h5 class="modal-title" id="exampleModalLabel"><?php echo $rowsQuiz['title']; ?></h5>
                              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                              </button>
                            </div>
                            <div class="modal-body">
                              <iframe src="<?php echo "https://www.knsinstitute.com/dseEer736djf83byv6tdbttci/assets/quiz/".$rowsQuiz['file']; ?>" height="600px" width="100%" scrolling="auto"></iframe>
                            </div>
                          </div>
                        </div>
                      </div>
                    <?php
                    
                  }
                  echo "</ul></div>";
                }
              ?>
              
              </div>
            </li>  
          <?php

          } }
          ?>
      </ul>

    </div>
  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
  .modal-dialog {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  min-width: 100%;
}

.modal-content {
  height: auto;
  min-height: 100%;
  border-radius: 0;
}
.cbtn{
    height: 30px;
    padding: 0px 15px 0px 15px;
}
.cbtn_n{
    height: 25x;
    padding: 0px 45px 0px 3px;
    border: none;
}
.listBook
{
  list-style: none;
}
.clockSize{
    font-size: 20px;
    margin-left: 40px;
    margin-right: 10px;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>