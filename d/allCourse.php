<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

?>
<style>
.navic{
    border-width:1px 0;
    list-style:none;
    margin:0;
    padding:0;
    text-align:center;
    position:relative;
}
.navic p {
    position:absolute;
    top:0;
    width:100%;
    text-align: left;
    padding: 20px 100px 20px 20px;
}
.navic h1 {
    position:absolute;
    top:0;
    width:100%;
    text-align: left;
    padding: 20px 100px 20px 20px;
}
</style>
<div class="content-wrapper">
  <div class="row">
    <div class="col-md-12 grid-margin">
      <div class="d-flex justify-content-between flex-wrap">
        <div class="d-flex align-items-end flex-wrap">
          <div class="mr-md-3 mr-xl-5">
            <h2>Welcome to KnS Institute's Online Portal</h2>
            <p class="mb-md-0">Available Courses</p>
          </div>
        </div>

      </div>
    </div>
  </div>

  <div class="container">
    <div class="row">
      <?php
      $query = "SELECT * FROM class";
      $result = mysqli_query($connection, $query);
      $id = 1;
      while ($rows = mysqli_fetch_array($result)) {

        ?>
        <div class="navic">
          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="262.5pt" height="165.000002pt" viewBox="0 0 262.5 165.000002" version="1.2">
            <defs>
              <clipPath id="clip1">
                <path d="M 2.875 0 L 259.808594 0 L 259.808594 164.007812 L 2.875 164.007812 Z M 2.875 0 " />
              </clipPath>
            </defs>
            <g id="surface1">
              <g clip-path="url(#clip1)" clip-rule="nonzero">
                <path style=" stroke:none;fill-rule:nonzero;fill:rgb(130 177 68);fill-opacity:1;" d="M 28.570312 0 C 14.179688 0 2.875 11.296875 2.875 25.675781 L 2.875 138.644531 C 2.875 153.023438 14.179688 164.320312 28.570312 164.320312 L 167.3125 164.320312 C 181.703125 164.320312 193.007812 153.023438 193.007812 138.644531 L 193.007812 25.675781 C 193.007812 11.296875 181.703125 0 167.3125 0 Z M 259.808594 17.007812 L 203.285156 47.175781 L 203.285156 117.140625 L 259.808594 147.308594 Z M 259.808594 17.007812 " />
              </g>
            </g>
          </svg>
          <h1> <?php echo $rows['title']; ?> </h1>
          <p><?php echo $rows['desc']; ?></p>
        </div>
        <div class="col-md-6 col-lg-4 column">
          <div class="cardNew gr-2">
            <a href='level.php?classID=<?php echo $rows['id']; ?>' class="nav-link">
              <div class="txt">
                <h1> <?php echo $rows['title']; ?> </h1>
                <p><?php echo $rows['desc']; ?></p>
              </div>
            </a>
            <div class="ico-card">
              <i class="fa fa-video-camera"></i>
            </div>
          </div>
        </div>








      <?php }  ?>


    </div>
  </div>
</div>
<!-- /page content -->
<style>
  .makepointer {
    cursor: pointer;
  }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>