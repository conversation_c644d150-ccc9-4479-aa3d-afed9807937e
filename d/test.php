<?php
include("includes/header.php");
include("includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_REQUEST['id'])) {
    $id = mysqli_real_escape_string($connection, $_REQUEST['id']);
    $lectureTopic = $general->getLectureTopic($id);
} else {
    echo notify("Some error occured please try again.");
    exit;
}
$courseID = $general->getCourseIDFeomLectureID($id);
if (!$general->isEnrolled(userid, $courseID)) {
    echo notify("Sorry you are not enrolled in this course.");
    exit;
}
?>
<!-- page content -->
<div class="content-wrapper">

    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex align-items-end flex-wrap">
                    <div class="mr-md-3 mr-xl-5">
                        <h2>Test of <?php echo $lectureTopic; ?></h2>

                    </div>
                </div>
                <div class="align-right"><h3><a href="recording.php?courseID=<?php echo $courseID; ?>"><i class="mdi mdi-subdirectory-arrow-left"></i> Back</a></h3></div>

            </div>
        </div>
    </div>
    <div class="col-lg-12 grid-margin stretch-card">
              <div class="card">
                <div class="card-body">
                     <div class="table-responsive">
                    <table class="table">
                      <thead>
                        <tr>
                          <th>Test</th>
                          <th>File</th>
                          <th>Download</th>
                        </tr>
                      </thead>
                      <tbody>
                      <?php
                        $query = "SELECT * FROM test where lectureid=$id";
                        // echo  $query;
                        $result = mysqli_query($connection, $query);
                        if(mysqli_num_rows($result)>0){
                        while ($rows = mysqli_fetch_array($result)) {
                            $hTitle = $rows['title'];
                        ?>

                        <tr>
                          <td><?php echo $rows['title']; ?></td>
                          <td><?php echo $rows['file']; ?></td>
                          <td><a class="badge badge-success" href="assets/uploads/<?php echo $rows['file']; ?>"><i class="mdi mdi-download menu-icon"></i> &nbsp;Download</a></td>
                        </tr>
                        <?php } ?>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

    
        
        

            <?php }   ?>
       

</div>
<!-- /page content -->
<style>
    .makepointer {
        cursor: pointer;
    }
</style>
<?php
$footerJS = '<script>
function goToURL(url) {
  location.href = url;

}
</script>';
include("includes/footer.php");
?>