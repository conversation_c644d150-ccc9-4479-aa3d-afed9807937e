language: node_js
node_js:
  - "node"
  - "8"
  - "7"
  - "6"
  - "5"
  - "4"
sudo: false

env:
  global:
    - secure: "M4STT2TOZxjzv3cZOgSVi0J4j6enrGjgE+p5YTNUw+S6Dg6FS5pTIPeafu1kGhfm7F0uk7xPVwGKYb+3uWNO9IhkKXz8jySMMQ2iKISHwECGNNuFNjSMD1vIjbIkLwV8TyPO/PurQg2s+WtYz+DoAZsDFKpdaRUtif64OjdQ3vQ="
    - secure: "V+i7kHoGe7VXWGplPNqJz+aDtgDF9Dh9/guoaf2BPyXLvkFW6VgMjJyoNUW7JVsakrWzAz2ubb734vAPDt7BCcFQ2BqfatOmabdFogviCaXC6IqVaEkYS2eSP7MIUPIeWJgnTrDGzkFMDk4K7y5SiVJ8UmYwZxe+tJV7kbb0Yig="

before_install: if [[ `npm -v` == 2* ]]; then npm i -g npm@3; fi

install:
  - npm install
  - npm install -g grunt-cli

script: grunt build:travis

git:
  depth: 10

# TODO: Fix problem with coveralls.io not displaying autogenerated files
# after_success: npm run coveralls
