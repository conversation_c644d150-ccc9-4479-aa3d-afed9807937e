{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBlockStatements": true, "requireParenthesesAroundIIFE": true, "requireSpacesInConditionalExpression": true, "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true, "beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "requireMultipleVarDecl": "onevar", "requireBlocksOnNewline": true, "disallowPaddingNewlinesInBlocks": true, "disallowEmptyBlocks": true, "disallowSpacesInsideObjectBrackets": false, "disallowSpacesInsideArrayBrackets": true, "disallowSpacesInsideParentheses": true, "requireCommaBeforeLineBreak": true, "disallowSpaceAfterPrefixUnaryOperators": ["++", "--", "+", "-", "~", "!"], "disallowSpaceBeforePostfixUnaryOperators": ["++", "--"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireCamelCaseOrUpperCaseIdentifiers": "ignoreProperties", "disallowKeywords": ["with"], "disallowMultipleLineStrings": true, "validateIndentation": 4, "disallowTrailingWhitespace": true, "disallowTrailingComma": true, "requireLineFeedAtFileEnd": true, "requireCapitalizedConstructors": true}