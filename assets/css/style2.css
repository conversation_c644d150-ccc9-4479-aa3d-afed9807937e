select.form-control, .dataTables_wrapper select {
    outline: 1px solid #5d5656;
}
.form-control, .dataTables_wrapper select {
    border: 1px solid #5d5656;
}

.auth .brand-logo img {
    width: 100%;
    margin-top: -65px;
    margin-bottom: -60px;
}
.navbar .navbar-brand-wrapper .navbar-brand-inner-wrapper .navbar-brand img {
    height: 107px;
    
}
.content-wrapper .card{
    background-color: rgb(64,96,50);
}
.content-wrapper{
    background: #b8d877;
}
.content-wrapper1
{
    background: rgb(64,96,50);
    background: linear-gradient(0deg, rgba(64,96,50,1) 37%, rgba(184,216,119,1) 100%);
}
.sidebar{
    background: #406032;
}
.sidebar .nav .nav-item .nav-link .menu-title 
{
    color: white;
}
.sidebar .nav .nav-item .nav-link i.menu-icon
{
    color: white;
}
.sidebar .nav .nav-item.active > .nav-link i, .sidebar .nav .nav-item.active > .nav-link .menu-title, .sidebar .nav .nav-item.active > .nav-link .menu-arrow {
    color:white;
}
.navbar .navbar-brand-wrapper
{
    background: #406032;
}
.navbar .navbar-menu-wrapper
{
    background: #406032;
}
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link::after
{
    color:white;
}
.navbar .navbar-menu-wrapper .navbar-nav .nav-item.nav-profile .nav-link .nav-profile-name
{
    color:white;
}
/* .auth .auth-form-light
{
    background: #b8d877;
} */
.auth .brand-logo {
    margin-bottom: 0rem;
}
.btn-kns {
    color: #000;
    background-color: #b8d877;
    border-color: #000;
}
.btn-kns:hover {
    color: #000;
    background-color: #b8d877;
    border-color: #000;
}
.btn-primary {
    color: #fff;
    background-color: #406032;
    border-color: #000;
}
.btn-primary:hover {
    color: #fff;
    background-color: #406032;
    border-color: #000;
}
.text-youtube {
    color: #b8d877;
}
i.text-youtube {
    font-size: 2.875rem;
}
h6.text-youtube {
    font-size: 20px;
}
p.card-text{
    font-size: 16px;
    color: #fff !important;
}
.newsbox{
    padding: 10px;
}
.news
{
    background-color: #b8d877;
    padding: 5px;
}

.newsbox h6{
    color: #fff;
}
.news h6
{
    font-size: 16px;
    text-decoration: underline;
}
.news p
{
    text-align: justify;
}
.sidebar .nav {
    margin-bottom: 10px;
}
.table
{
    color: #fff !important;
}
.table2
{
    color: #000 !important;
}
.content-wrapper .card2 {
    background-color: #fff;
}

.align-right h3 a
{
    color: black;
    text-decoration: none;
    font-size: 2rem;
}


/* New Cards */

.gr-1 {
    background: linear-gradient(170deg, #01E4F8 0%, #1D3EDE 100%);
  }

  .gr-2 {
    background: linear-gradient(170deg, #c0fd55 0%, rgb(64 96 50) 100%);
  }

  .gr-3 {
    background: linear-gradient(170deg, #C86DD7 0%, #3023AE 100%);
  }

  * {
    transition: 0.5s;
  }

  

  .column {
    padding-left: 3rem;
  }

  .column {
    padding-left: 0;
  }

  

  .column:hover .cardNew .txt h1,
  .column:hover .cardNew .txt h5,
  .column:hover .cardNew .txt p {
    color: white;

  }

  .cardNew {
    min-height: 222px;
    margin-bottom: 20px;
    padding: 0.7rem 1rem;
    border: none;
    border-radius: 0;
    color: black;
    letter-spacing: 0.05rem;
    font-family: "Oswald", sans-serif;
    box-shadow: 0 0 21px rgba(0, 0, 0, 0.27);
  }

  .cardNew .txt {
    z-index: 1;
  }

  .cardNew .txt h1 {
    font-size: 1.5rem;
    font-weight: 300;
    /* text-transform: uppercase; */
    line-height: 32px;
  }

  .cardNew .txt p {
    font-size: 1rem;
    font-family: "Open Sans", sans-serif;
    letter-spacing: 0rem;
    margin-top: 13px;
    color: white;
    overflow: hidden;
   text-overflow: ellipsis;
   display: -webkit-box;
   -webkit-line-clamp: 3; /* number of lines to show */
   -webkit-box-orient: vertical;
  }

  .cardNew a {
    z-index: 3;
    font-size: 0.7rem;
    color: black;
    position: relative;
    bottom: -0.5rem;
    
  }

  .cardNew a:after {
    content: "";
    display: inline-block;
    height: 0.5em;
    width: 0;
    margin-right: -100%;
    margin-left: 10px;
    border-top: 1px solid white;
    transition: 0.5s;
  }

  .cardNew .ico-card {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 95%;
    height: 95%;
    overflow: hidden;
  }

  .cardNew i {
    position: relative;
    right: -50%;
    top: 60%;
    font-size: 12rem;
    line-height: 0;
    opacity: 0.2;
    color: white;
    z-index: 0;
  }
  /* end new cards */