import requests
from pytube import Playlist, YouTube
from pytube.exceptions import VideoUnavailable

def on_progress(stream, chunk, bytes_remaining):
	print("Downloading...  =>")

def on_complete(stream, file_path):
	print("Uploading...")

#151
shares = "m9hdlUykxY4,EipJfcM_IjI,SQGC2me0CQ4,rrBVf9CxfnU,8AdISeK24l4,KJoYCK4vyWg,srlQ0WxLiUM,W8reV_hfNz8,gh2zusiFBk4,6Ls3QaKMSYg,jmTWOY13O_Y,wbGKs11jHfk,O1ouHZ-H9Sc,PbsJH7_dbvg,VrGCPYoYKpM,pBC2NsNcjbA,5rIsNPRFtTQ,WMoCSHiK5VM,kwqS_434cbM,Hn4lNEmtJlQ,lW1svR-sP2A,PPaoeWKtai0,CpH72_fZH<PERSON>,nThdT_99Qr4,RfUG6RzlmLU,z6WwPKHnp90,Cj1olMipXz8,bxHBXGBBgns,Fh5ZIBL73Oo,j4VXmBXfYXI,ta-jLl4dLJI,vUOldjmmdj0,yUFyexSP78g,EnriOW-0f-Y,KCbru2KrWaQ,kgcMVQiBNfY,_4A1aEFPp4o,-GE88DKNw04,e2p5uBTnwW0,iBdFfwupqjo,V0e-Y7RwseI,gmyaE5RmYvU,sxSLQCpnROA,JS8Zt5bRwW4,gCLCTgo7Yio,2jeUKbGFlVI,s5EblI8wnOc,oOE1YttRSE8,J7tGQ8TokD4,Ig4BdvUJxn0,KDlGY365-pM,Et01JyPewD0,Bl152ltY2EQ,xq78hTymBnw,FTYaQvTZ3mg,2hvrbjTST1U,oyweadAjSa8,mI7u3o_eMe0,vAkpEXq3SBc,5ATu7dsodY4,eyvI7zV2A38,fXZG-_oIQYk,0RRvq5wQfSM,aD8b-JO4VwI,nLNqXMKjv-Y,xu4N45A9JMg,6xbKPIRXOaM,7eG6SMBI2d0,TiIp6ZCGbW0,7C1mpn1b3-s,DzVGBUInrE8,nvNAfYgVkYM,dJ9K-Af3gsU,GC3L-u7SqT0,TiIp6ZCGbW0,7C1mpn1b3-s,DzVGBUInrE8,nvNAfYgVkYM,dJ9K-Af3gsU,GC3L-u7SqT0,m9hdlUykxY4,EipJfcM_IjI,49Jm6UXq6RY,WycWWKS3UWg,Go_1InDSVcs,Y2Y8gu7k5y4,DOpxe1K5EPk,jG0BC0C6VI4,rfqtmUxYl4o,5BfUt9RqVqc,n4v6kgDB2_I,AZiHkCl_S2Q,l1iLqwc6Yjo,M6luGIet_R8,yYIqbN-JdIk,GuTdiVP-TzM,oQOo-2fMkSg,LmY2ayS5u8Q,vD2PUS0jbi8,Ne4MfTYTca4,LOTeIFfuFq4,NgKDTDS-1JY,homz2AQ9QdA,76JAUhF0CIs,XPfzjXbJSSY,2F_MM8MmqBE,i_UafDhzlf4,9EbY4snbp0w,Xj6E6RI56Wk,cj5pZ9MKBkA,3HjtI0JF7CI,XDtBGYs_R8k,q70p1fxo8oU,RIQU1g5-2vU,4KmBrqlpn7E,YAOmo2XtMaw,zJAt4vXCitA,vJ94gnveRA0,vH4gINjq_E0,QVuVXtoWRlw,47qROlPQ__A,02ssYiCoayY,fL5st5zusTs,Qa9YKTfVujU,a9lTkZExuVA,DOixb1TcICo,yZZe1P-DuhE,CbaYen-c458,KvYZ4CGz2ZI,pUYiE6y6I98,dhsjpRDg6Lg,yDbIA4LR1WE,V-RrBPtz9MM,2m3BkVVfZyA,JIV-0DBkmjU,_5jdeYTX5Wo,dAuK8RFew38,SgriXrmb_T4,qauXOBy1Is4,qauXOBy1Is4,zfe-saIWWXc,A0jzGC99oVs,_5MdVJGRrQ4,guZJ-l54jaE,u65193TjEsM,hXUzlk6ScbU,uHMHgJDiwYQ,yq3vMnGckkM,hXUzlk6ScbU,uHMHgJDiwYQ,yq3vMnGckkM,LkDcIBmFrTM,6W_l1nXtCkI,1-930ANRicA,Vowlk12OC0A,i7KprDB0Cks,5mXmy3Xnv48,CcchqJz5b4g,sZ8x-VUIBnU,U8TDBwfflG4,ypb7LLziwdk,z_rdPTXGCK4,BX1ULNHO2O4,6zP2T9_mZKM,bdVU99xIIDg,WtruZnzIJ0I,e_OXqizB9rM,Bl152ltY2EQ,xq78hTymBnw,FTYaQvTZ3mg,2hvrbjTST1U"

for username in shares.split(','):
    print(username)
    response = requests.get('http://localhost/yt/fn.php?video='+username)
    if response.status_code == 200:
        print('file already exist')
        continue
    else:
        print('Downloading...')
        try:
            yt = YouTube("https://www.youtube.com/watch?v="+username,on_progress_callback=on_progress,on_complete_callback=on_complete,use_oauth=False,allow_oauth_cache=False)
            yt.streams.filter(file_extension='mp4', res="720p").first().download(filename=username+'.mp4')
            
            try:
                params = {"video":username}
                r = requests.get("http://localhost/yt/we3.php", params=params)
                if r.status_code == 200:
                    print(r.content)
            except Exception:
                print("server ERROR!!")

        except VideoUnavailable:
            print("server ERROR!!")

        except pytube.exceptions.RegexMatchError:
            print('The Regex pattern did not return any matches for the video:')

        except pytube.exceptions.ExtractError:
            print ('An extraction error occurred for the video:')

        except pytube.exceptions.VideoUnavailable:
            print('The following video is unavailable:')
            