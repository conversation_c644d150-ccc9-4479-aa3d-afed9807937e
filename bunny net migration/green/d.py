import requests
from pytube import Playlist, YouTube
from pytube.exceptions import VideoUnavailable

def on_progress(stream, chunk, bytes_remaining):
	print("Downloading...  =>")

def on_complete(stream, file_path):
	print("Uploading...")

#151
shares = "oyweadAjSa8,mI7u3o_eMe0,vAkpEXq3SBc,5ATu7dsodY4,eyvI7zV2A38,fXZG-_oIQYk,0RRvq5wQfSM,aD8b-JO4VwI,nLNqXMKjv-Y,xu4N45A9JMg,6xbKPIRXOaM,7eG6SMBI2d0,7eG6SMBI2d0,eD97fHoJaJs,r4gIlFp6wKQ,eD97fHoJaJs,r4gIlFp6wKQ,eD97fHoJaJs,r4gIlFp6wKQ,AMepQgb1b_g,AMepQgb1b_g,-X92xUdPGVo,99F7QolKYy4,AMepQgb1b_g,-X92xUdPGVo,99F7QolKYy4,-X92xUdPGVo,99F7QolKYy4,EDMCFsFS1Q4,pAkGXw_GIzA,Hb2uql-PfBM,JFpgRvTlIhI,JFpgRvTlIhI,Hb2uql-PfBM,pAkGXw_GIzA,pAkGXw_GIzA,Hb2uql-PfBM,JFpgRvTlIhI,9FJdXXthNq4,9FJdXXthNq4,9FJdXXthNq4,8z2D1laxNO0,8z2D1laxNO0,8z2D1laxNO0,GLmlxG5WuSs,GLmlxG5WuSs,GLmlxG5WuSs,GkB1vNwzaEY,l31Lr-FWYdE,tHPGG_BKI-Q,6fywQdK7uNk,ge8T3BDNN9Q,GkB1vNwzaEY,l31Lr-FWYdE,tHPGG_BKI-Q,6fywQdK7uNk,ge8T3BDNN9Q,GkB1vNwzaEY,l31Lr-FWYdE,tHPGG_BKI-Q,6fywQdK7uNk,ge8T3BDNN9Q,zDHNqpUaKyQ,zDHNqpUaKyQ,zDHNqpUaKyQ,HF9BnsDyCYU,-d1iwJQrRlo,-d1iwJQrRlo,-9j2mC69DPg,-d1iwJQrRlo,CcAG4gkxGhE,DzVGBUInrE8,nvNAfYgVkYM,dJ9K-Af3gsU,GC3L-u7SqT0,hXUzlk6ScbU,uHMHgJDiwYQ,yq3vMnGckkM,m9hdlUykxY4,EipJfcM_IjI,AMepQgb1b_g,-X92xUdPGVo,99F7QolKYy4,TiIp6ZCGbW0,7C1mpn1b3-s,yyD9wiY3NNw,M4lOpInUi0Q,AtA1UZVb30w,gj-_HpLZCVA,FDICLPhMwxo,E_63i4hxehk,iAJLQncEoJc,G4hFs4a9TLs,Cpwbs4obB60,afGu9F2qzAA,2Tl-qMwmSDY,1_dn6ie7JAQ,ZtbXH8AeVI0,Fol3i3n9F2M,2x2C6XVtu5A,3FlQ7pgU0xo,-hBO1SIWw4Y,EOiYPKK3Bsg,dg7o3XLc-BY,MOCK,QYl2RFQaX-w,uCPP0aqTVhk,sadfasd,caas,OFWD5DHKbvo,RTwrVt4BouU,HmttA1v3xqE,e_anYZpgeNI,kH3jW_CabEQ,jPWu7Vu7bn4,EhC7Zwnsk5M,M-8DPnAmF5I,pFKUuFyjyo4,BdagM8FqQds,haD_nez_hhM,oUVZ7cWa4LQ,XhD_LfpUfZA,HvIL96RJiEU,sUxT9ZihFAk,rsXbxQO8TAM,Hq5rmSTphIU,XIrvrESSQvs,Ymikx4GhKB8,OgWWSZo0P4s,BvCwpHDby9s,qcj70wlAlWI,zNcWFCMSPxU,X6LmBlnU3vw,CXbcYnRtljk,6EEgi5L9msA,aIxfDa_okL4,owAIr0uu8WY,H4bPv7ciqGE,ol7-mPvpXjw,ViSv6c0I9qw,wrk_wgAb_Y8,6OCxzOzEDuI,8NNLRN7TQkw,yz7HVJN4L28,d8ajaBrVlO0,hAOTDnFMVlU,d8ajaBrVlO0,hAOTDnFMVlU,7hFm1oh3NcU,0Q28dF0j9gI,JgiFGcm2el8,tMLKEICdXsw,QLcSLRx_njE,REnvrPGXIzM,4blkWwDNv4I,439lJQYfVRk,aVxUsSesIkg,UROC4lZ5Pyg,mLEPxNakbVQ,aK_CLmpVJNY,9K-TdRY5I8E,9Wc1KyjvXZc,DzqrLr0LAB8,efR_XbA41qg,OU8Rvj4a9eI,lsbVeRwre-c,2qEU75jT-k4,7G6i3eLPh7c,8Ym_0pIJ9_w,yMPee8RQdmc,kRZoA2Gt8jU,vlTu-pkf-KQ,1kMDv9i0bDw,OsY24285ny0,9aTkocfJsis,LTsjbKzq2gA,FyixaV7yqTM,gDJ1DeFai-I,eDFPMetBSuc,bGV-Eyin02A,ziw0cRUKiNQ,W5aO5q1bCTI,1gTj2FPIWak,dQbJksQmGXc,GWQcs-ypZFo,i1FAzZuI9II,D_oCuasl9Aw,u59Jv19xwxw,ORFCmTuJk5Y,9ha1fK1Hv4g,VJEQDP0obrY,hbpM87GccjM,Zfj1KFnEE4o,6t3ehhbDjys,cK6L5Z_UL_w,EQFpf6uLjoE,md9DhSHXxgQ,UEX2QTHtiGk,wl2yAxZgkCE,dsfOLKNm2zU,i6_JDmH_BNQ,ZluadXy8wcA,AEbanxr-vSU,qR9v--1Jv0s,ArlF9_JNsy0,lE6__FEpbbk,1suMn7XEMzs,ArlF9_JNsy0,ztHpaRaf_8w,eUIO_6yIbBQ,eUIO_6yIbBQ,t-HLTPrgF64,Av-9ehCxX5Y,XrsboQmQXlE,zyUQe0TEpmo,U4LzqYHTgK8,YVCYFad5IH0,LIiJgMZ5D8M,_wAktLxT2ig,odC9ZKr9Wi8,eJYoRGRKEtY,GmL8-61UNV8,sPocGkWWlOA,xR573cUEPdI,sfwo1H2sIXw,hc5F_V8fBo4,t0QJZlM0Snk,98AKFetTPW4,keXO2sCOC3s,jfdeV_evZK8,s8eIHRoJ4MI,4dTw0WhQDu4,-38qU4P7T-8,Tf-Oakbc3rc,cCDPHefus1Q,KOSTkrTu1f4,tHKozok6VEc,r-Np3v27gsU,XDnloIx9s0U,b9n-1DvBeDc,AQjhDq570SM,uFUuT-pwXzA,JqkScD_BDSo,mqkBk5jihcA,bJyOjayrZmw,U7Dx50EceSU,sBVYXQtGAxw,hYBdXuPFkOs,bjiHdOeu5V0,dD920EdNgoU,1gT1gy4PvVk,y1RzWLjsTTw,oJ6TxHG95tw,GpSbC7mzVkc,bG8RDY4P7Ok,zgzhDmZXf9Y,sKE4OJQYAnU,iULYYBeXxc0,sRxZeLNu5Jc,-IhDCXlpnTk,QOtcSPgI_yo,8lNCgXuP9jc,XMJtQetvHdc,7h7g-uDh8AI,VAAzlizfqww,Bh0cRwKwaX4,1jL5HitSc5c,1qd-i6HBqq4,vpJidHSJU2g,cJfm80pjm2k,KlizSpl__3c,MKWdwrqPwUI"

for username in shares.split(','):
    print(username)
    response = requests.get('http://localhost/yt/fn.php?video='+username)
    if response.status_code == 200:
        print('file already exist')
        continue
    else:
        print('Downloading...')
        try:
            yt = YouTube("https://www.youtube.com/watch?v="+username,on_progress_callback=on_progress,on_complete_callback=on_complete,use_oauth=False,allow_oauth_cache=False)
            yt.streams.filter(file_extension='mp4', res="720p").first().download(filename=username+'.mp4')
            
            try:
                params = {"video":username}
                r = requests.get("http://localhost/yt/we3.php", params=params)
                if r.status_code == 200:
                    print(r.content)
            except Exception:
                print("server ERROR!!")

        except VideoUnavailable:
            print("server ERROR!!")

        except pytube.exceptions.RegexMatchError:
            print('The Regex pattern did not return any matches for the video:')

        except pytube.exceptions.ExtractError:
            print ('An extraction error occurred for the video:')

        except pytube.exceptions.VideoUnavailable:
            print('The following video is unavailable:')
            