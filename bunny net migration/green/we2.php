<?php


include("bunnycdn-storage.php");



function UR_exists($url){
	$headers=get_headers($url);
	return stripos($headers[0],"200 OK")?true:false;
}
$video = $_GET['video'];


if(UR_exists("https://knsinstitute1.b-cdn.net/".$video

.".mp4"))
{
	echo "This page exists";
	unlink($video.".mp4");
}
else
{
	$bunny = new BunnyCDNStorage("knsinstitute","7fc25f6e-45cb-442a-81f656cf5289-421f-4e15","de");


	echo $bunny->uploadFile($video.".mp4","/knsinstitute/".$video.".mp4");


	unlink($video.".mp4");
}
	



?>