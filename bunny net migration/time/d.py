import requests
from pytube import Playlist, YouTube
from pytube.exceptions import VideoUnavailable

def on_progress(stream, chunk, bytes_remaining):
	print("Downloading...  =>")

def on_complete(stream, file_path):
	print("Uploading...")

#shares = ""
shares = "L0p3nK5BsUQ,ja0jixj7krw"

for username in shares.split(','):
    print(username)
    response = requests.get('http://localhost/yt/fn.php?video='+username)
    if response.status_code == 200:
        print('file already exist')
        continue
    else:
        print('Downloading...')
        try:
            yt = YouTube("https://www.youtube.com/watch?v="+username,on_progress_callback=on_progress,on_complete_callback=on_complete,use_oauth=False,allow_oauth_cache=False)
            yt.streams.filter(file_extension='mp4', res="720p").first().download(filename=username+'.mp4')
            
            try:
                params = {"video":username}
                r = requests.get("http://localhost/yt/we3.php", params=params)
                if r.status_code == 200:
                    print(r.content)
            except Exception:
                print("server ERROR!!")

        except VideoUnavailable:
            print("server ERROR!!")

        except yt.exceptions.RegexMatchError:
            print('The Regex pattern did not return any matches for the video:')

        except yt.exceptions.ExtractError:
            print ('An extraction error occurred for the video:')

        except yt.exceptions.VideoUnavailable:
            print('The following video is unavailable:')
            