import requests
from pytube import Playlist, YouTube
from pytube.exceptions import VideoUnavailable,RegexMatchError,ExtractError


def on_progress(stream, chunk, bytes_remaining):
    print("Downloading...  =>")


def on_complete(stream, file_path):
    print("Uploading...")


# shares = ""
shares = "U4LOMmsrsoM,9EF5bXVlcLg,QGa4hI4naj4,Qhlt7KMq2uo,LtUhd3QAxHQ,ewPoMERn5rU,6Zx7LyojhG4,tE8izCY1leM,ZoAlF8GxGeE,jymBBwxI6jo,Era4g1A24Ig,hV-twMs1im8,eqcQhPR30ig,UNyv90NaN8M,ZSUVuu8fSHo,f18fhfy7r0E,8UEYiKZRe2Q,eW21L0_uYTU,-rukRdCjoHE,vPMsABxwtew,uh8WdVxr8Cc,3nxGxT7Pgic,TnN5O-9jwew,PCbMjrWlbvU,CcNK3oAdEvM,mhaDggHYS1E,wTnp58UX6z8,nwp4pL-GU8A,WNNZf2Dk47o,0HsOQiHrT90,pgWCCTJrBsE,5GgaTZQLeHA,_sElaYbOx-M,OizHC6PtMUM,F1lW_TmW4QA,mSK1LEPTxsM,--ZEQsP0yVk,i_zeuYPe3fE,KNcg25T8NHc,ZQ6wo5MUW-Q,6op8VMyWOfA,5FyGi8zHcOU,aVxUsSesIkg,UROC4lZ5Pyg,mLEPxNakbVQ,Sn-OxztYYY4,MPSXki1HaAw,0jBUPEO4dA4,8Yv7P6fTXaU,zbbAXQTI8x4,2LSeWMdvxeY,pKh5hs3e7i0,ezGmmjypwuQ,bSAlGw_xM1U,q3p893TaSqw,EM7UZOxC_fU,ncEREXaBZBE,2gQalABig-Q,Q7Pe3-1TGY0,MHeKo2qghk4,MlvyM5HzJs8,QFf85cKe2-4,Uk-pSI6nHXA,oMO-mZJXDio,SovRTFcpl3o,gNkRYlEzSkc,w7zns0HdYNk,MjhVVBIThTc,MlcLxPMUCKE,aK_CLmpVJNY,9K-TdRY5I8E,9Wc1KyjvXZc,DzqrLr0LAB8,efR_XbA41qg,OU8Rvj4a9eI,lsbVeRwre-c,2qEU75jT-k4,7G6i3eLPh7c,8Ym_0pIJ9_w,yMPee8RQdmc,kRZoA2Gt8jU,vlTu-pkf-KQ,1kMDv9i0bDw,OsY24285ny0,9aTkocfJsis,LTsjbKzq2gA,FyixaV7yqTM,gDJ1DeFai-I,eDFPMetBSuc,bGV-Eyin02A,ziw0cRUKiNQ,W5aO5q1bCTI,1gTj2FPIWak,dQbJksQmGXc,GWQcs-ypZFo,i1FAzZuI9II,D_oCuasl9Aw,u59Jv19xwxw,ORFCmTuJk5Y,9ha1fK1Hv4g,VJEQDP0obrY,hbpM87GccjM,Zfj1KFnEE4o,6t3ehhbDjys,cK6L5Z_UL_w,EQFpf6uLjoE,PpAGfjuc5eU,xiaNrYsfilg,vLYdUmivk0c,ieQvvzVwp9o,md9DhSHXxgQ,UEX2QTHtiGk,hliFwuWPmkY,IBztVyfckjg,a-qej10twLw,FfUpq5rnrhs,1fS-CRERE50,W1liETgdpcQ,tvf9vKpqRhg,4IijpIodKUA,OavF5xr1o7Q,fJmH1bmxhZE,TI4D4C_OvbM,_ghSLlUU27Y,RN3rCpy8S7Q,zuPsr5P-i_A,2QzM1K63h_s,O7GegZoy7xw,bp4TMULZKrg,WgjHvx2REQg,QXasGI2kHgY,8C6V45xN20k,_v2-DnUE2jo,3FIVz6E2HF4,HFBy3Er8sMc,qHXKfDIMpdQ,gX42nw40AMc,JvE0Y35B3tg,UYlzLQKYDSk,wl2yAxZgkCE,dsfOLKNm2zU,i6_JDmH_BNQ,ZluadXy8wcA,AEbanxr-vSU,qR9v--1Jv0s,ArlF9_JNsy0,lE6__FEpbbk,1suMn7XEMzs,ArlF9_JNsy0,ztHpaRaf_8w,eUIO_6yIbBQ,eUIO_6yIbBQ,t-HLTPrgF64,Av-9ehCxX5Y,XrsboQmQXlE,gQweXs9RgDg,9sDAFeplCjA,GF7ORpXZ_vA,M1Pfu3jrXbc,2rLloGJiLjU,bwsLATEdBbk,2Hu_mt8TWmM,XoBq42ML5hQ,75868,y908,uio,a,zxa,86798,oV_9sCNLg30,I26DKnTjEQc,Qg563TAVW1Y,aAbYdLRl2X8,L-bmJbg3CmQ,DLhIae6ueZs,yZvyp1PIOtI,mbumZVTVhDU,wi910tq_D3U,aNhoqVH23gg,UgfcCAtB94k,eaqYgfUiYQg,iXM4Or91vK4,PefAR-3AQLo,IZ8xy82eMuM,c0Jn9NDIKJQ,iwUmALQV7Ok,HbU21ikm7yA,-ZqV_WdEfmU,_fpY3TT3BN4,3NGzX_fozMk,4PtCce7KERY,fzNgG7Gcyxk,pEf5w4-jemg,-hYSpm3_4Q4,IMpmhyGF6b0,FgEjrwDERVM,SHiyMcflmnU,N53ZDPRqyh8,IMpmhyGF6b0,su_7LvWD6jk,L2HAwTl142k,ccYMQlA7jMc,9Kzb_8zViTQ,NgvrfIZotKw,uGanxhbaQWg,fasKTw3uxzk,lrg1hlDEUWU,SXZLCQc-CoA,vYMazf_Mlrg,jzoDOxutSUo,JwlO-tagTb4,mjwIiyI2YHc,dQpVmsnSzTM,niISBBmowcE,LBTv3QSWz-s,t8MJZzXQWuM,Wx0JHPz6MQo,mTZqeLZaRBk,dkwaxRl9UQA,Xns9_WFfWbE,wdBFV-nFcMM,3XvB5T-hPDE,dkwaxRl9UQA,J9I99WC7myM,KWDoKx3v1lY,RFOqVgdsDUM,EffXX__Fzhg,tRXQRyQkTrs,qfbfxs01w7E,yACUzu_9Ens,-8nP4CUQ29Y,cLVJLjVdSRY,tZ63LXlgMww,jN2etlAJDL0,sBoE1ZurRO4,7IltFVxbFYY,EuXeAGMMM1w,uGV-WkZLQRs,SDiakH6IOOE,_YioZOY_U-s,TS8vwat8-u8,QwcAkbRZWBs,99jP9gBdQWk,EJrWZLaQXA4,hJ6nC7OzJdA,u7kXhq7l0Ao,CatU6g897uY,r05wT8t8HvY,aB_EWE7j9oQ,AJ0-Opv_12o,SM1EwkPcPR4,Z_MdTbh7bzg,oJxL3T6g7dg,UbaZMEqBKas,HpYKhOVnKb8,luveKG-GdRI,CdIoloh-Dz8,3XIoIa9VCEI,mDaOr-HnDg4,fMqGxhiBqaE,acoiPuvBE1I,1RHHU6iLzZQ,K-w4eGS3u3I,iNgMcta3A8A,7axNsduY7S0,wyORGNHJ89U,9tEbGSiUjOE,3cagXcJwCTQ,Sd6_cuYwwW4,AV8NCIq5FDA,zkTwz963cdA,4PtCce7KERY,fzNgG7Gcyxk,pEf5w4-jemg,-hYSpm3_4Q4,AthIGBZc-4Y,_0tOliwhvIw,yW0w736rmKw,AaWnw24_NUc,9bIbY8_nTsw,r22MN25XazA,CnkQBbMRad4,omp-zMfbiow,s2yNXKp_yJo,EGyMpniEfow,E83tOd_DE1Y,t8NcCVq4IMM,w-C51GGAcvM,lbext_ciYao,Nb7AutBQ9fE,kpzrK_gRic4,cA_h_AAIEcw,_qggSE7ngq8,eo2o8JveRUU,OpuQDQKHjKE,yF0KLl87guw,Dyv1OddvYEs,U8BLrwttnKk,xao6xOHrNBA,u7MW6_G2CBE,7vJfNT3mwRY,RV-Hwrkiobk,DaA_raLLa9Y,a6bXzPW37p8,VfTwK-PzLNI,BJ393IhHbZ4,gS_rKdJf9s8,sSrmgzw5z4g,Z0bHwTThVn8,zyUQe0TEpmo,U4LzqYHTgK8,YVCYFad5IH0,7VGYmb343gQ,LIiJgMZ5D8M,_wAktLxT2ig,njDWqh611O0,o9buwPuXadM,UzupPCPLEK0,RuTuL7AKPMc,p4zqguaAkV4,YZaRO0D1JcQ,Vh7dJlT0Y5g,5wbQN9J6kY8,Uq813syN-Ng,iok0Xkh9GBk,JaOrayZDerY,buoKwjk95hY,aFtvQGrfFF8,CKcOGroW2Ew,Tg076BuSoos,hnJHAO_evmU,FcS5DAz8Cv4,tw5oMmbIgeg,wxv_HwPTBWo,7Z8oVQOYlzo,o9buwPuXadM,UzupPCPLEK0,RuTuL7AKPMc,p4zqguaAkV4,YZaRO0D1JcQ,Vh7dJlT0Y5g,5wbQN9J6kY8,Uq813syN-Ng,iok0Xkh9GBk,JaOrayZDerY,odC9ZKr9Wi8,8jkccMVloz4,u7MW6_G2CBE,OQddMgOHULw,L57V7o4uz3c,ODCE73bUtP8,wK3rd2oFGkI,FxLEDd6H4s0,qnqWU_0XuYE,OwF4Taphgzs,m7YdEnQVGlc,8WQ9CX8WTA0,2xewgFMAaos,_TxfZVVx7_s,84wTwm3Yy4g,AtGScGn27us,OEaCHPkCpjI,CkWIxmJylJI,89YWaKKRdy8,vJAJAQggqkw,n9n--a3cCEE,Hx7xmDLpGPk,HMUGIPb0u4s,ykK4yI0ZP0c,m-0JXPJwdTI,W6o0z2KQR-E,hGfD51sM8QM,qeJOvBLzgio,dUqCVrwQJZE,cflLj9qwuj8,pg4xZujOGxg,5AHA4ro7mcE,eJYoRGRKEtY,pFrJ1_2J9ak,DQ0KcgtJNTM,6WVAHbq0COo,WW8ZbleO7M0,mpGw1eK_af0,ApfOwVXM9O0,GmL8-61UNV8,sPocGkWWlOA,xR573cUEPdI,sfwo1H2sIXw,hc5F_V8fBo4,k8h1BbCBfXE,G4dJ8r0rHqg,4xLuyXsTOxM,1KBo9BPtfOQ,pg4xZujOGxg,FxLEDd6H4s0,cQT9_IDIRso,c008wDS0v1U,4xLuyXsTOxM,_k4Mbjux7fM,PA7C6zq_quE,tNHUZGhLbHU,4E4H3wQ1qWI,n_Ni-dPSyew,5kPoJgL5mi8,wyE6eCpHNKE,llNc5S4iBA8,EJG_glTjFP8,MvcQSk3yT1M,oW_5CfQpRXc,_qxon7YATsQ,0p_heCKmyiw,tvECgCIqKo8,U_eLVNylGRA,0x0NxL-bjdQ,-iS_7gycJ5I,Zaa1sk7RJbk,li_l6Vo5QCQ,v3QPbFqJ-dc,CPeWG4id_zw,nd6oHp3NVj0,62PS1vCFbuA,kLlstFHth9I,8jlzdfvUVAM,8_XwsrKo0zQ,z0om5VOeRaI,Tf5FZs_IbR4,Kjvk7nFXCUo,AAHK1jmOlII,_5KwWN9xRPo,JW8-GaqSnVE,lleVFk5z5lI,HL1Zh2G3pFE,ZTYdJjkSGXU,XhjJOkMtUNk,t0QJZlM0Snk,kUofMa_NGwQ,gXud0Q1Eo8Y,pbGXFKVy8tA,om_xYa1aPjc,_15FePt0kqI,_Wxsxi3zpLg,RgMCenXDoY0,QWNTTJhiCNE,MpLMy5H8R6M,wTsqUy0xqfk,lmIY8YF8fNg,ptZWS9Fth28,y3V-M1mUy24,0gjeWdDzJZk,98AKFetTPW4,keXO2sCOC3s,jfdeV_evZK8,76B2YUTQOyU,s8eIHRoJ4MI,1P0LYpRhDlM,DPYg6FMKE-o,-8nwghtiSrg,9ajjpOb0P84,BQ4O3XUnmFI,ZXcrXtl0bB0,BaElrqQGMBc,4dTw0WhQDu4,UHvinkgRDaI,TCXBflKXjUU,2eJ4zW3KOgg,j3D9bR5CGzE,Handouts,TdP-SI46K1I,F0X_3VEL-Zo,-38qU4P7T-8,lX2Jp-a0uHM,Tf-Oakbc3rc,cCDPHefus1Q,pYM03iiEimk,qZ1s1fcDc6M,9BIEo97QGBo,GuhIBbPRxnw,6T-GYTgxlbs,KOSTkrTu1f4,tHKozok6VEc,KKFMXoPIuAM,r-Np3v27gsU,bDAxWvFMeK4,W1OZj0fVn9g,tBChrYwPxr0,vONQMgVFjXI,0eeEjKqfI8A,-b1n5Vz2QlE,NwPEhI6O0MM,IyKqe2H48A8,W7ZoDC01qiE,sY9YSUmrZ-M,6bQx-pFX96Q,u1N1epDEkqY,XDnloIx9s0U,gR7Avmm_tB4,w6mA_x7sStI,EnOBZySnV9A,679utrq7w88,nNVSyd9G0w0,gqgfd9jNh7M,b9n-1DvBeDc,s0qSdfKvRbU,Urinjk7IBMU,BAL_LqaWoZU,6UDDAhve1Vg,InV3_J30JYQ,X1EGPm3BxbE,BgRGGmO1itk,lQ4ahQ-Ytq0,A4rDcxBfwic,AQjhDq570SM,uFUuT-pwXzA,zH8vg0p0F28,EBdhLBJM0Hs,3ssEeB9n_NI,prPegiVzQ2A,pIcFc3EFVGs,TIYLvXQ-1eM,HbM6XstkIpY,4Ts69xg8s-8,lq9OEQXZ3IM,qEXu_cDkq8k,u9_tARmTwMI,VbYnVrKo668,N25H57cBaL8,0BizBqa7ZL0,gMoiqvB8ta0,wYSuhEwm4Co,KnQ68IAG5jA,EU0lXkCpd1I,yCBSxcNLyqQ,_wlWgFfQnTM,JqkScD_BDSo,mqkBk5jihcA,bJyOjayrZmw,U7Dx50EceSU,sBVYXQtGAxw,i02N1ScXQtQ,0MOk52Et7OU,XxjBaazULzA,ptIzRi9wy-g,gguQa5qPTzk,T4H6RCn2pFI,_TPaChZtcZk,VoRdG0v1Gz0,hYBdXuPFkOs,bjiHdOeu5V0,dD920EdNgoU,1gT1gy4PvVk,ESE5kcdEHw4,arETD41XUTM,l5gTJ9ArMYE,DuKAsO6c2eQ,KwfjCW3RaRE,z,y1RzWLjsTTw,cvYgU8_d6o8,0o43D45pm8Q,XshWoDzApq0,W6SrMjqpHLg,W4X4a9pK-Bk,hYXBnp0E2Rk,o4tV41N11ho,TV3I5JJF-nE,mt5WMMm_8mo,x6NjAZfFKVM,iLAh6TtnlOk,5lgDxs5iVTU,pGT_o8rRPSQ,oJ6TxHG95tw,GdWKgXKWL3w,8fYoS7Rb44A,Imbf4nJ5E9I,FKR5gAbsJjU,UZbuo4CNwU8,emBUgn0MsDo,torZuedXxdM,JhJd1Eu9W4g,_M_jxLkMBtA,DkmEMnAfUpQ,2P1Oi5D0TIc,GpSbC7mzVkc,bG8RDY4P7Ok,zgzhDmZXf9Y,sKE4OJQYAnU,iULYYBeXxc0,sRxZeLNu5Jc,w-5ggmRTgyU,p_vHwAJgvDw,Uql_uk5WSH4,zJezFs8Pd4Y,X9K4NuYDL_M,2ujNn0OzHQY,Th2f8mwx0NE,3m1SV-ZCkp0,95mzOaOmqoI,F7PLV0QuFbU,fZriNi9mYU4,BAbZhBNLaKg,v1RRIYsXYFw,uEVvQkHHs0g,n6aQ6hf9X1s,rdh9PpzAj-I,zKWD0dQM8hQ,kKhLIeRiTVU,sBuGjd0iyjY,PIdrzZmDUKM,WW5ok2J6dvY,B3alVmNi4_o,IfeQrI5TmYw,-IhDCXlpnTk,Qfd9Zd6aHJQ,7tnSAlTyG9c,shkLBtR9l9w,L0p3nK5BsUQ,QOtcSPgI_yo,8lNCgXuP9jc,B6sc5vyKYaM,4NP58y3Vbec,zVM2Lue_2Ac,5IZIuBJAMbs,ja0jixj7krw,XMJtQetvHdc,YRjUcbSfmDQ,7h7g-uDh8AI,VAAzlizfqww,a1gv6e5IN7E,F5Sk2ET-WoU,pjd1xX7F_P4,Bh0cRwKwaX4,14BF4DMl0Wk,aS8vz9BmlaM,NDNN5bT5yEE,qvMKPCec1Ds,MFQxeyJxQZU,2cBRFQyl22c,tX8dwLxfVto,4NP58y3Vbec,gfEQGLPTD6w,HHHQtClHvMk,tsFTCiL0Js4"

for username in shares.split(','):
    print(username)
    response = requests.get('http://localhost/yt/fn.php?video='+username)
    if response.status_code == 200:
        print('file already exist')
        continue
    else:
        print('Downloading...')
        try:
            yt = YouTube("https://www.youtube.com/watch?v="+username, on_progress_callback=on_progress,
                         on_complete_callback=on_complete, use_oauth=False, allow_oauth_cache=True)
            if yt.streams.get_highest_resolution().resolution >= "720":
                yt.streams.filter(file_extension='mp4', res="720p").first().download(
                    filename=username+'.mp4')
            else:
                yt.streams.filter(file_extension='mp4', res=yt.streams.get_highest_resolution(
                ).resolution).first().download(filename=username+'.mp4')

            try:
                params = {"video": username}
                r = requests.get("http://localhost/yt/we3.php", params=params)
                if r.status_code == 200:
                    print(r.content)
            except Exception:
                print("server ERROR!!")

        except VideoUnavailable:
            print("server ERROR!!")

        except RegexMatchError:
            print('The Regex pattern did not return any matches for the video:')

        except ExtractError:
            print('An extraction error occurred for the video:')
